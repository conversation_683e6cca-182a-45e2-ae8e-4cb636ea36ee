#!/usr/bin/env python3
"""
Simple demo script to run the LAS ML pipeline test.
This demonstrates the complete workflow from LAS loading to model training and evaluation.
"""
import os
import sys
import time
from pathlib import Path

def check_dependencies():
    """Check if all required dependencies are available."""
    required_packages = [
        'torch', 'numpy', 'matplotlib', 'pandas', 'sklearn', 'yaml', 'tqdm', 'seaborn'
    ]

    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print("Missing required packages:")
        for pkg in missing_packages:
            print(f"  - {pkg}")
        print("\nPlease install missing packages:")
        print("  pip install -r requirements.txt")
        return False

    return True

def main():
    """Main demo function."""
    print("LAS to ML Pipeline Demo")
    print("="*50)

    # Check dependencies first
    print("Checking dependencies...")
    if not check_dependencies():
        return 1

    # Check if LAS files exist
    las_dir = os.path.join(os.path.dirname(__file__), 'Las')
    if not os.path.exists(las_dir):
        print(f"Error: LAS directory not found at {las_dir}")
        print("Please ensure LAS files are available in the 'Las' folder.")
        return 1

    las_files = [f for f in os.listdir(las_dir) if f.lower().endswith('.las')]
    if not las_files:
        print(f"Error: No LAS files found in {las_dir}")
        return 1

    print(f"Found {len(las_files)} LAS files:")
    for f in las_files:
        file_path = os.path.join(las_dir, f)
        file_size = os.path.getsize(file_path) / 1024  # KB
        print(f"  - {f} ({file_size:.1f} KB)")

    # Run the pipeline test
    print("\nStarting the ML pipeline test...")
    print("This may take several minutes depending on your hardware...")

    start_time = time.time()

    try:
        from test_las_ml_pipeline import LASMLPipelineTest

        # Initialize and run test with output directory
        pipeline_test = LASMLPipelineTest(
            output_dir="output",
            enable_interactive=True  # Enable interactive display
        )
        results = pipeline_test.run_complete_test()

        elapsed_time = time.time() - start_time

        print(f"\nDemo completed successfully in {elapsed_time:.1f} seconds!")
        print("\nGenerated output files:")

        # Check output directory structure
        output_dir = Path("output")
        output_files = []

        # Check visualizations
        viz_dir = output_dir / "visualizations"
        if viz_dir.exists():
            for viz_file in viz_dir.glob("*.png"):
                output_files.append((str(viz_file), f'Visualization: {viz_file.stem}'))

        # Check models
        model_dir = output_dir / "models"
        if model_dir.exists():
            for model_file in model_dir.glob("*.pth"):
                output_files.append((str(model_file), f'Trained model: {model_file.stem}'))

        # Check results
        results_dir = output_dir / "results"
        if results_dir.exists():
            for result_file in results_dir.glob("*.json"):
                output_files.append((str(result_file), f'Results: {result_file.stem}'))

        for filename, description in output_files:
            if os.path.exists(filename):
                file_size = os.path.getsize(filename) / 1024  # KB
                print(f"  ✓ {filename} ({file_size:.1f} KB) - {description}")
            else:
                print(f"  ✗ {filename} - Not created")

        # Print summary of results
        if 'evaluation' in results:
            eval_results = results['evaluation']
            print(f"\nModel Performance Summary:")
            print(f"  • Test RMSE: {eval_results.get('rmse', 'N/A'):.4f}")
            print(f"  • Test R²: {eval_results.get('r2', 'N/A'):.4f}")
            print(f"  • Test Correlation: {eval_results.get('correlation', 'N/A'):.4f}")

        return 0

    except ImportError as e:
        print(f"Import error: {e}")
        print("Please ensure all dependencies are installed:")
        print("  pip install -r requirements.txt")
        return 1
    except Exception as e:
        print(f"Error during pipeline execution: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)