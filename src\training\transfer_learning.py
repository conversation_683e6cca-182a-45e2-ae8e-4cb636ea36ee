import torch
import numpy as np
import os
from typing import Dict, <PERSON><PERSON>, Optional
from tqdm import tqdm


class TransferLearningHelper:
    """Helper class for two-stage transfer learning.
    
    Implements the transfer learning approach from <PERSON> et al. (2024):
    1. Stage 1: Pretrain on physics-derived targets 
    2. Stage 2: Finetune on true targets
    
    This approach helps the model learn physics-consistent relationships
    before fine-tuning on actual data, improving generalization.
    """
    
    def __init__(self, config: dict, trainer, model, loss_fn):
        """Initialize transfer learning helper.
        
        Args:
            config: Full configuration dict containing 'transfer_learning' section
            trainer: PhysicsGuidedTrainer instance
            model: Neural network model to train
            loss_fn: Loss function to use for training
        """
        self.config = config.get('transfer_learning', {})
        self.trainer = trainer
        self.model = model
        self.loss_fn = loss_fn
        
        # Validate configuration
        self._validate_config()
        
        # Ensure output directory exists
        os.makedirs(os.path.dirname(self.config['pretrained_path']), exist_ok=True)
        
    def _validate_config(self):
        """Validate transfer learning configuration."""
        required_keys = ['pretrain_epochs', 'pretrain_lr', 'finetune_epochs', 'finetune_lr']
        missing_keys = [key for key in required_keys if key not in self.config]
        
        if missing_keys:
            raise ValueError(f"Missing required transfer learning config keys: {missing_keys}")
        
        # Set default values for optional keys
        self.config.setdefault('early_stopping_patience', 10)
        self.config.setdefault('save_pretrained', True)
        self.config.setdefault('pretrained_path', 'output/models/pretrained_bigru.pth')
        
    def run_transfer_learning(self, train_loader, val_loader, preprocessor=None):
        """Run complete two-stage transfer learning.
        
        Args:
            train_loader: Training data loader
            val_loader: Validation data loader
            preprocessor: Data preprocessor for unit conversion (optional)
            
        Returns:
            Dict with training results and metrics
        """
        print("\n" + "="*60)
        print("STARTING TRANSFER LEARNING (TWO-STAGE TRAINING)")
        print("="*60)
        
        results = {}
        
        # Stage 1: Pretrain on physics targets
        pretrain_loss = self.pretrain_stage(train_loader, val_loader, preprocessor)
        results['pretrain_loss'] = pretrain_loss
        
        # Stage 2: Finetune on true targets  
        finetune_loss = self.finetune_stage(train_loader, val_loader)
        results['finetune_loss'] = finetune_loss
        
        print("\n" + "="*60)
        print("TRANSFER LEARNING COMPLETED")
        print("="*60)
        print(f"Best Pretrain Loss (physics targets): {pretrain_loss:.4f}")
        print(f"Best Finetune Loss (true targets):    {finetune_loss:.4f}")
        print("Note: Pretrain and finetune losses use different targets and are not directly comparable.")
        
        # Report a meaningful improvement metric within Stage 2 (fine-tuning only)
        stage2_start_loss = None
        try:
            if hasattr(self, 'finetune_history') and len(self.finetune_history) > 0:
                stage2_start_loss = self.finetune_history[0]['val_loss']
        except Exception:
            stage2_start_loss = None
        
        if stage2_start_loss is not None and stage2_start_loss > 0:
            stage2_improvement = (stage2_start_loss - finetune_loss) / stage2_start_loss * 100.0
            print(f"Stage-2 improvement (finetune best vs epoch-1 val): {stage2_improvement:+.1f}%")
        else:
            print("Stage-2 improvement: N/A")
        
        return results
        
    def pretrain_stage(self, train_loader, val_loader, preprocessor=None):
        """Stage 1: Pretrain on physics-derived targets.
        
        This stage teaches the model to predict physics-consistent VS values
        from VP using the rock physics model. This provides a good initialization
        for the neural network weights.
        """
        print("\n=== STAGE 1: PRETRAINING ON PHYSICS TARGETS ===")
        print(f"Epochs: {self.config['pretrain_epochs']}")
        print(f"Learning Rate: {self.config['pretrain_lr']}")
        print(f"Rock Physics Model: {self.trainer.rock_physics_model.name}")
        
        # Create optimizer for pretraining
        optimizer = torch.optim.Adam(
            self.model.parameters(), 
            lr=self.config['pretrain_lr']
        )
        
        best_val_loss = float('inf')
        pretrain_history = []
        
        for epoch in range(self.config['pretrain_epochs']):
            # Training with physics targets
            epoch_loss = 0.0
            num_batches = 0
            
            self.model.train()
            
            # Use tqdm for progress bar
            pbar = tqdm(train_loader, desc=f"Pretrain Epoch {epoch+1}/{self.config['pretrain_epochs']}")
            
            for batch_features, batch_targets in pbar:
                batch_features = batch_features.to(self.trainer.device)
                
                # Compute physics targets for this batch
                with torch.no_grad():
                    physics_targets = self.trainer.compute_physics_predictions(
                        batch_features, preprocessor
                    )
                    physics_targets = torch.tensor(
                        physics_targets, 
                        dtype=torch.float32,
                        device=batch_features.device
                    ).unsqueeze(-1)  # Add channel dimension to match model output
                
                # Forward pass
                optimizer.zero_grad()
                predictions = self.model(batch_features)
                
                # Use physics targets instead of true targets
                loss = self.loss_fn(predictions, physics_targets)
                
                # Backward pass
                loss.backward()
                optimizer.step()
                
                epoch_loss += loss.item()
                num_batches += 1
                
                # Update progress bar
                pbar.set_postfix({'Loss': f'{loss.item():.4f}'})
            
            # Validation
            val_metrics = self._validate_physics(val_loader, preprocessor)
            avg_train_loss = epoch_loss / num_batches
            
            print(f"  Train Loss: {avg_train_loss:.4f} | Val Loss: {val_metrics['loss']:.4f} | Val RMSE: {val_metrics['rmse']:.4f}")
            
            # Track history
            pretrain_history.append({
                'epoch': epoch + 1,
                'train_loss': avg_train_loss,
                'val_loss': val_metrics['loss'],
                'val_rmse': val_metrics['rmse']
            })
            
            # Save best model
            if val_metrics['loss'] < best_val_loss:
                best_val_loss = val_metrics['loss']
                if self.config['save_pretrained']:
                    torch.save(
                        self.model.state_dict(), 
                        self.config['pretrained_path']
                    )
                    print(f"  → Saved pretrained model (val_loss: {best_val_loss:.4f})")
        
        # Store pretraining history
        self.pretrain_history = pretrain_history
        
        print(f"\nPretraining completed. Best validation loss: {best_val_loss:.4f}")
        return best_val_loss
    
    def finetune_stage(self, train_loader, val_loader):
        """Stage 2: Finetune on true targets.
        
        This stage fine-tunes the pretrained model on actual VS measurements,
        allowing it to correct physics model errors while maintaining the
        physics-consistent initialization.
        """
        print("\n=== STAGE 2: FINETUNING ON TRUE TARGETS ===")
        print(f"Epochs: {self.config['finetune_epochs']}")
        print(f"Learning Rate: {self.config['finetune_lr']}")
        print(f"Early Stopping Patience: {self.config['early_stopping_patience']}")
        
        # Load pretrained weights if saved
        if self.config['save_pretrained']:
            try:
                self.model.load_state_dict(
                    torch.load(self.config['pretrained_path'], map_location=self.trainer.device)
                )
                print("✓ Loaded pretrained weights")
            except Exception as e:
                print(f"⚠ Warning: Could not load pretrained weights: {e}")
                print("  Continuing with current weights")
        
        # Create optimizer with reduced learning rate for fine-tuning
        optimizer = torch.optim.Adam(
            self.model.parameters(), 
            lr=self.config['finetune_lr']
        )
        
        best_val_loss = float('inf')
        patience_counter = 0
        finetune_history = []
        
        for epoch in range(self.config['finetune_epochs']):
            # Standard training on true targets
            train_loss = self.trainer.train_epoch(train_loader, optimizer, self.loss_fn)
            val_metrics = self.trainer.evaluate(val_loader, self.loss_fn)
            
            print(f"  Epoch {epoch+1}/{self.config['finetune_epochs']}: "
                  f"Train Loss: {train_loss:.4f} | "
                  f"Val Loss: {val_metrics['loss']:.4f} | "
                  f"Val RMSE: {val_metrics['rmse']:.4f} | "
                  f"Val Corr: {val_metrics['correlation']:.4f}")
            
            # Track history
            finetune_history.append({
                'epoch': epoch + 1,
                'train_loss': train_loss,
                'val_loss': val_metrics['loss'],
                'val_rmse': val_metrics['rmse'],
                'val_correlation': val_metrics['correlation']
            })
            
            # Early stopping and model saving
            if val_metrics['loss'] < best_val_loss:
                best_val_loss = val_metrics['loss']
                patience_counter = 0
                
                # Save best fine-tuned model
                best_model_path = 'output/models/best_model.pth'
                torch.save(self.model.state_dict(), best_model_path)
                print(f"  → Saved best model (val_loss: {best_val_loss:.4f})")
            else:
                patience_counter += 1
                if patience_counter >= self.config['early_stopping_patience']:
                    print(f"  Early stopping triggered at epoch {epoch+1}")
                    break
        
        # Load best model
        try:
            best_model_path = 'output/models/best_model.pth'
            self.model.load_state_dict(
                torch.load(best_model_path, map_location=self.trainer.device)
            )
            print("✓ Loaded best fine-tuned model")
        except Exception as e:
            print(f"⚠ Warning: Could not load best model: {e}")
        
        # Store finetuning history
        self.finetune_history = finetune_history
        
        print(f"\nFine-tuning completed. Best validation loss: {best_val_loss:.4f}")
        return best_val_loss
    
    def _validate_physics(self, val_loader, preprocessor):
        """Validate using physics targets.
        
        This validation compares model predictions against physics-derived targets
        rather than true measurements, which is appropriate for the pretraining stage.
        """
        self.model.eval()
        total_loss = 0.0
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch_features, batch_targets in val_loader:
                batch_features = batch_features.to(self.trainer.device)
                
                # Get physics targets
                physics_targets = self.trainer.compute_physics_predictions(
                    batch_features, preprocessor
                )
                physics_targets = torch.tensor(
                    physics_targets, 
                    dtype=torch.float32,
                    device=batch_features.device
                ).unsqueeze(-1)  # Add channel dimension
                
                # Predict
                predictions = self.model(batch_features)
                loss = self.loss_fn(predictions, physics_targets)
                
                total_loss += loss.item()
                all_predictions.extend(predictions.cpu().numpy().flatten())
                all_targets.extend(physics_targets.cpu().numpy().flatten())
        
        # Compute metrics
        all_predictions = np.array(all_predictions)
        all_targets = np.array(all_targets)
        
        rmse = np.sqrt(np.mean((all_predictions - all_targets) ** 2))
        
        return {
            'loss': total_loss / len(val_loader),
            'rmse': rmse
        }
    
    def get_training_history(self):
        """Get complete training history from both stages.
        
        Returns:
            Dict containing pretraining and fine-tuning histories
        """
        return {
            'pretrain_history': getattr(self, 'pretrain_history', []),
            'finetune_history': getattr(self, 'finetune_history', [])
        }
    
    def plot_training_history(self, save_path='output/transfer_learning_history.png'):
        """Plot training history from both stages.
        
        Args:
            save_path: Path to save the plot
        """
        try:
            import matplotlib.pyplot as plt
            
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('Transfer Learning Training History', fontsize=16)
            
            # Pretrain history
            if hasattr(self, 'pretrain_history'):
                pretrain_df = self.pretrain_history
                epochs = [h['epoch'] for h in pretrain_df]
                train_losses = [h['train_loss'] for h in pretrain_df] 
                val_losses = [h['val_loss'] for h in pretrain_df]
                val_rmses = [h['val_rmse'] for h in pretrain_df]
                
                axes[0, 0].plot(epochs, train_losses, 'b-', label='Train Loss')
                axes[0, 0].plot(epochs, val_losses, 'r-', label='Val Loss')
                axes[0, 0].set_title('Pretraining: Loss')
                axes[0, 0].set_xlabel('Epoch')
                axes[0, 0].set_ylabel('Loss')
                axes[0, 0].legend()
                axes[0, 0].grid(True)
                
                axes[0, 1].plot(epochs, val_rmses, 'g-', label='Val RMSE')
                axes[0, 1].set_title('Pretraining: RMSE')
                axes[0, 1].set_xlabel('Epoch')
                axes[0, 1].set_ylabel('RMSE')
                axes[0, 1].legend()
                axes[0, 1].grid(True)
            
            # Finetune history
            if hasattr(self, 'finetune_history'):
                finetune_df = self.finetune_history
                epochs = [h['epoch'] for h in finetune_df]
                train_losses = [h['train_loss'] for h in finetune_df]
                val_losses = [h['val_loss'] for h in finetune_df]
                val_rmses = [h['val_rmse'] for h in finetune_df]
                val_corrs = [h['val_correlation'] for h in finetune_df]
                
                axes[1, 0].plot(epochs, train_losses, 'b-', label='Train Loss')
                axes[1, 0].plot(epochs, val_losses, 'r-', label='Val Loss')
                axes[1, 0].set_title('Fine-tuning: Loss')
                axes[1, 0].set_xlabel('Epoch')
                axes[1, 0].set_ylabel('Loss')
                axes[1, 0].legend()
                axes[1, 0].grid(True)
                
                axes[1, 1].plot(epochs, val_rmses, 'g-', label='Val RMSE')
                axes[1, 1].plot(epochs, val_corrs, 'm-', label='Val Correlation')
                axes[1, 1].set_title('Fine-tuning: Metrics')
                axes[1, 1].set_xlabel('Epoch')
                axes[1, 1].set_ylabel('Metric Value')
                axes[1, 1].legend()
                axes[1, 1].grid(True)
            
            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Training history plot saved to: {save_path}")
            
        except ImportError:
            print("Warning: matplotlib not available, cannot plot training history")
        except Exception as e:
            print(f"Warning: Could not create training history plot: {e}")