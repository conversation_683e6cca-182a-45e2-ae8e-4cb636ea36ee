import numpy as np
from typing import Union, Dict, Any
from .base import RockPhysicsModel

class EmpiricalVPVS(RockPhysicsModel):
    """Empirical VP-VS relationship: VS = a * VP^b
    
    Based on <PERSON><PERSON><PERSON> et al. (1985) mudrock line generalization.
    Default parameters from <PERSON> et al. (2024) Equation 6.
    
    This model implements the power law relationship between P-wave and S-wave velocities
    commonly observed in sedimentary rocks.
    """
    
    def __init__(self, a: float = 0.8619, b: float = 0.8621, name: str = "empirical_vpvs"):
        """
        Initialize the empirical VP-VS model.
        
        Args:
            a: Multiplicative coefficient (default from paper)
            b: Power coefficient (default from paper)
            name: Model name identifier
        """
        super().__init__(name=name, a=a, b=b)
        self.a = a
        self.b = b
        self._fitted = True  # Uses empirical coefficients by default
    
    def predict(self, vp: Union[np.ndarray, Dict[str, np.ndarray]], **kwargs) -> np.ndarray:
        """
        Predict VS from VP using power law relationship.
        
        Args:
            vp: P-wave velocity in km/s. Can be:
                - np.ndarray: Direct VP values
                - Dict: Must contain 'VP' key with velocity values
            **kwargs: Additional parameters (ignored for this model)
            
        Returns:
            vs: S-wave velocity in km/s
            
        Raises:
            ValueError: If vp is a dict without 'VP' key
            TypeError: If vp is neither ndarray nor dict
        """
        # Handle different input types
        if isinstance(vp, dict):
            if 'VP' not in vp:
                raise ValueError("Dictionary input must contain 'VP' key")
            vp_values = np.asarray(vp['VP'])
        elif isinstance(vp, np.ndarray):
            vp_values = vp
        else:
            # Try to convert to numpy array
            try:
                vp_values = np.asarray(vp)
            except:
                raise TypeError("Input must be numpy array or dict with 'VP' key")
        
        # Ensure positive velocities for power law
        vp_values = np.maximum(vp_values, 1e-8)
        
        # Apply power law: VS = a * VP^b
        vs_values = self.a * np.power(vp_values, self.b)
        
        return vs_values
    
    def fit(self, features, vs: np.ndarray, **kwargs) -> None:
        """
        Fit coefficients to well-specific data using linear regression in log space.
        
        The relationship VS = a * VP^b becomes linear in log space:
        log(VS) = log(a) + b * log(VP)
        
        Args:
            features: P-wave velocity training data. Same format as predict()
            vs: S-wave velocity training data in km/s
            **kwargs: Additional fitting parameters (ignored)
        """
        # Extract VP from features
        if isinstance(features, dict):
            if 'VP' not in features:
                raise ValueError("Dictionary input must contain 'VP' key")
            vp = np.asarray(features['VP'])
        else:
            vp = np.asarray(features)
            
        # Ensure positive velocities
        vp_clean = np.maximum(vp.flatten(), 1e-8)
        vs_clean = np.maximum(vs.flatten(), 1e-8)
        
        # Remove any invalid values
        valid_mask = np.isfinite(vp_clean) & np.isfinite(vs_clean) & (vp_clean > 0) & (vs_clean > 0)
        vp_clean = vp_clean[valid_mask]
        vs_clean = vs_clean[valid_mask]
        
        if len(vp_clean) < 2:
            raise ValueError("Need at least 2 valid data points for fitting")
        
        # Log transform for linear regression
        log_vp = np.log(vp_clean)
        log_vs = np.log(vs_clean)
        
        # Linear regression: log_vs = log_a + b * log_vp
        # Using numpy's least squares solver
        A = np.vstack([log_vp, np.ones(len(log_vp))]).T
        coeffs, residuals, rank, s = np.linalg.lstsq(A, log_vs, rcond=None)
        
        # Extract coefficients
        self.b = coeffs[0]  # Power coefficient
        log_a = coeffs[1]   # Log of multiplicative coefficient
        self.a = np.exp(log_a)
        
        # Update parameters
        self.params.update({'a': self.a, 'b': self.b})
        self._fitted = True
        
        # Store fitting quality metrics
        if len(residuals) > 0:
            self.fit_rmse = np.sqrt(residuals[0] / len(log_vs))
        else:
            # Calculate manually if residuals not returned
            predicted_log_vs = log_a + self.b * log_vp
            self.fit_rmse = np.sqrt(np.mean((log_vs - predicted_log_vs)**2))
    
    def get_equation(self) -> str:
        """Return the model equation as a string."""
        return f"VS = {self.a:.4f} × VP^{self.b:.4f}"
    
    def get_fit_quality(self) -> Dict[str, Any]:
        """Return fitting quality metrics if available."""
        metrics = {'fitted': self._fitted}
        if hasattr(self, 'fit_rmse'):
            metrics['rmse_log_space'] = self.fit_rmse
        return metrics