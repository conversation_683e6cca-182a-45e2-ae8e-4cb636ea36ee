# Comprehensive Implementation Guide: Physics‑Guided BiGRU with Minimal Transfer Learning

This guide operationalizes Implementation_Plan_Simple.md into concrete steps with prerequisites, exact commands, validation, troubleshooting, and time estimates. It keeps the current pipeline stable by default and adds a config‑gated, two‑stage transfer learning routine.

## Phase Overview and Navigation

- Phase 0 — Environment Setup and Prerequisites (15–30 min)
- Phase 1 — Baseline Verification (30–60 min)
- Phase 2 — Immediate Improvements (config, minimal TL, guardrails, metrics, A/B) (~3–6 hours)
- Phase 3 — Future Enhancements (pluggable physics, full pseudolabels, TL trainer, eval, orchestration) (~3–4 weeks)

Table of Contents
- [Phase 0 — Environment Setup and Prerequisites](#phase-0--environment-setup-and-prerequisites)
- [Phase 1 — Baseline Verification](#phase-1--baseline-verification)
- [Phase 2 — Immediate Improvements](#phase-2--immediate-improvements)
  - [2.1 Configuration Additions](#21-configuration-additions-transfer-learning--guardrails)
  - [2.2 Implement Minimal Two-Stage TL](#22-implement-minimal-two-stage-transfer-learning-in-examplestrain_modelpy)
  - [2.3 Optional: Enable TL in LAS Pipeline](#23-optional-enable-tl-in-the-las-pipeline-test)
  - [2.4 Stability Guardrails (Tests)](#24-stability-guardrails-tests)
  - [2.5 Evaluation-Time Physics Sanity Metrics](#25-evaluation-time-physics-sanity-metrics)
  - [2.6 A/B Experiments and Reporting](#26-ab-experiments-and-reporting)
  - [2.7 Documentation Updates](#27-documentation-updates)
- [Phase 3 — Future Enhancements](#phase-3--future-enhancements-build-on-phase-2-stabilityfirst)
  - 3.1 Pluggable Rock Physics Architecture
  - 3.2 Full Pseudolabel Integration (Config‑Gated)
  - 3.3 TransferLearningTrainer Class
  - 3.4 Advanced Evaluation Framework
  - 3.5 Feature Management & Validation
  - 3.6 Experiment Orchestration
  - 3.7 Guardrails, Rollback, and CI

---


---

## Phase 0 — Environment Setup and Prerequisites

> Summary
> - Objective: Prepare a clean, reproducible environment and verify tooling
> - Dependencies: None
> - Outputs: Activated venv, installed dependencies, passing smoke tests, PyTorch version printed
> - Time: 15–30 min


- OS: Windows 10/11 or Linux/macOS
- Python: 3.10+ recommended
- GPU: Optional (PyTorch auto‑detects CUDA)
- Tools: git, PowerShell (Windows) or bash (Linux/macOS)

Setup (PowerShell):

```powershell
python -m venv .venv
.\.venv\Scripts\Activate.ps1
python -m pip install --upgrade pip
pip install -r requirements.txt
pytest -q
python -c "import torch; print(torch.__version__)"
```

Setup (bash):

```bash
python3 -m venv .venv
source .venv/bin/activate
python -m pip install --upgrade pip
pip install -r requirements.txt
pytest -q
python - <<'PY'
import torch; print(torch.__version__)
PY
```

Expected: tests run, PyTorch prints a version (e.g., 2.2.0).

Estimated time: 15–30 min

[Back to top](#phase-overview-and-navigation)

---

## Phase 1 — Baseline Verification

> Summary
> - Objective: Confirm current pipeline stability and artifacts before changes
> - Dependencies: Phase 0
> - Outputs: Successful LAS demo run, artifacts under output/, baseline metrics captured
> - Time: 30–60 min


Run the existing LAS pipeline demo end‑to‑end to confirm the current baseline is stable.

```powershell
python run_las_demo.py
```

Validation:
- Exit code 0
- Output directories created: `output/visualizations`, `output/models`, `output/results`
- Printed metrics include RMSE, R², correlation (see “Model Performance Summary”)

If failures occur, see Troubleshooting.



Estimated time: 30–60 min (depends on hardware)
[Back to top](#phase-overview-and-navigation)


---

## Phase 2 — Immediate Improvements

> Summary
> - Objective: Introduce minimal, config‑gated improvements (TL, guardrails, metrics) without risking stability
> - Dependencies: Phase 1
> - Outputs: Transfer learning branch, guardrail test, physics sanity metrics, A/B artifacts
> - Time: ~3–6 hours


### 2.1 Configuration Additions (Transfer Learning & Guardrails)

Continue to use YAML configs with minimal, additive keys.

Edit `configs/default_config.yaml` and add (or merge) the following block:

```yaml
transfer_learning:
  enabled: false          # Default off; enables two‑stage TL when true
  pretrain_epochs: 8      # 5–10 recommended
  finetune_epochs: 40     # 20–50 recommended
  pretrain_lr: 0.001
  lr_reduction_factor: 0.1
  patience: 10
  save_pretrained_model: true
```

Notes:
- Keep `training.strategy` as is for now. We will branch on `transfer_learning.enabled` in the entry point to avoid wide code churn.
- Ensure existing keys remain unchanged to preserve baseline behavior.

Validation:
- Config loads without YAML errors in both `examples/train_model.py` and `test_las_ml_pipeline.py`.

Estimated time: 10–15 min

---

### 2.2 Implement Minimal Two‑Stage Transfer Learning in examples/train_model.py

Objective: Add a config‑gated branch that:
1) Pretrains on physics‑derived VS targets (km/s → m/s handled as needed)
2) Fine‑tunes on true labels with a reduced LR and early stopping

Where: `examples/train_model.py`

High‑level steps:
1. Load config and data (already present)
2. Compute VP index from config: `config['data']['features']` + `vp_feature_name`
3. If `transfer_learning.enabled` is true:
   - Stage A (Pretrain):
     - Derive `VS_phys` on‑the‑fly via rock physics:
       - Denormalize VP to m/s if using a preprocessor (not used in this synthetic example)
       - Convert VP m/s → km/s if required by physics model (`conversion_factor` in `physics_coupling`)
       - Call `rock_physics_model.predict(vp_km_s)` → VS in km/s, convert to m/s for consistency
     - Replace targets with `VS_phys` for this stage
     - Train `pretrain_epochs` with `pretrain_lr`
     - Optionally save checkpoint
   - Stage B (Finetune):
     - Reload checkpoint (if saved)
     - Reset optimizer with reduced LR: `pretrain_lr * lr_reduction_factor`
     - Train for `finetune_epochs` against true labels with early stopping (`patience`)
4. Else (default): run existing single‑stage path unchanged

Minimal code template (illustrative diffs):

```python
# After trainer/optimizer/loss_fn are created
if config.get('transfer_learning', {}).get('enabled', False):
    tl = config['transfer_learning']
    vp_idx = trainer.vp_index  # computed in trainer from config

    # --- Stage A: Pretrain on physics targets ---
    pretrain_opt = torch.optim.Adam(model.parameters(), lr=tl['pretrain_lr'])
    for epoch in range(tl['pretrain_epochs']):
        # Build physics targets per batch inside train loop (simplest):
        train_loss = trainer.train_epoch(train_loader, pretrain_opt, loss_fn)
        val_metrics = trainer.evaluate(val_loader, torch.nn.MSELoss())
        print(f"[Pretrain] Epoch {epoch+1}/{tl['pretrain_epochs']} | RMSE {val_metrics['rmse']:.3f}")
    if tl.get('save_pretrained_model', True):
        torch.save(model.state_dict(), 'output/models/pretrained_bigru.pth')

    # --- Stage B: Finetune on true labels ---
    if tl.get('save_pretrained_model', True) and os.path.exists('output/models/pretrained_bigru.pth'):
        model.load_state_dict(torch.load('output/models/pretrained_bigru.pth', weights_only=True))
    finetune_lr = tl['pretrain_lr'] * tl['lr_reduction_factor']
    finetune_opt = torch.optim.Adam(model.parameters(), lr=finetune_lr)
    best_val = float('inf'); patience = 0
    for epoch in range(tl['finetune_epochs']):
        train_loss = trainer.train_epoch(train_loader, finetune_opt, loss_fn)
        val_metrics = trainer.evaluate(val_loader, torch.nn.MSELoss())
        print(f"[Finetune] Epoch {epoch+1}/{tl['finetune_epochs']} | RMSE {val_metrics['rmse']:.3f}")
        if val_metrics['loss'] < best_val:
            best_val = val_metrics['loss']; patience = 0
        else:
            patience += 1
            if patience >= tl['patience']:
                print('Early stopping (finetune).'); break
else:
    # Existing single‑stage training loop (unchanged)
    ...
```

Implementation details:
- In this synthetic example, VP is already set to a realistic range. For real data with a preprocessor, denormalize VP to m/s, then convert to km/s before `rock_physics_model.predict` (see `src/training/trainer.py` for a reference implementation of unit handling under the `loss_function` strategy).
- Keep all default behaviors when `transfer_learning.enabled == false`.

Validation:
- With `enabled: false`, training logs and metrics match previous behavior
- With `enabled: true`, run completes for both stages, creates `output/models/pretrained_bigru.pth` (if save enabled)

Estimated time: 60–90 min

---

### 2.3 Optional: Enable TL in the LAS Pipeline Test

Where: `test_las_ml_pipeline.py`

Low‑churn approach:
- Set `config['transfer_learning']['enabled'] = true` and branch inside `_train_model` similarly: pretrain then finetune, using loaders already created there.
- Prefer writing results to `output/results/*.json` with both runs (A/B) for later comparison.

Validation:
- Demo (`python run_las_demo.py`) still completes with TL enabled
- JSON results show separate metrics for pretrain and finetune stages

Estimated time: 60–90 min

---

### 2.4 Stability Guardrails (Tests)

Add a test that locks core performance and unit/scale invariants.

Create `tests/test_stability_lock.py`:

```python
import numpy as np
from test_las_ml_pipeline import LASMLPipelineTest

def test_metrics_guardrails():
    t = LASMLPipelineTest(output_dir='output')
    results = t.run_complete_test()
    evalr = results.get('evaluation', {})
    assert evalr.get('rmse', 1e9) <= 80.0
    assert evalr.get('r2', 0.0) >= 0.96
```

Run:

```powershell
pytest -q tests/test_stability_lock.py
```

Estimated time: 30–45 min

---

### 2.5 Evaluation‑Time Physics Sanity Metrics

Goal: Improve observability without changing training.

Option A (low‑touch): Extend `test_las_ml_pipeline.py` to compute and log:
- corr(VS_phys, VS_true)
- corr(VS_phys, VS_ml_pred)

Template snippet (use the preprocessor to denormalize as in `_test_physics_guidance`):

```python
# After computing predictions_orig and targets_orig
vp_idx = preprocessor.get_feature_index(config['data'].get('vp_feature_name', 'P-WAVE'))
vp_kms = preprocessor.inverse_transform_features(test_features[..., [vp_idx]], [vp_idx]).squeeze(-1) / 1000.0
vs_phys_kms = rock_physics_model.predict(vp_kms)
vs_phys = vs_phys_kms * 1000.0
corr_phys_true = np.corrcoef(vs_phys.reshape(-1), targets_orig.reshape(-1))[0,1]
corr_phys_pred = np.corrcoef(vs_phys.reshape(-1), predictions_orig.reshape(-1))[0,1]
print(f"Physics alignment: corr(VS_phys, VS_true)={corr_phys_true:.3f}, corr(VS_phys, VS_pred)={corr_phys_pred:.3f}")
```

Option B: Create `src/evaluation/simple_evaluator.py` with a reusable `evaluate_physics_alignment(...)` helper and call it from the evaluation/reporting path.

Validation:
- Alignment metrics printed and optionally stored in `output/results/*.json`

Estimated time: 45–60 min

---

### 2.6 A/B Experiments and Reporting

Run two configurations:
- A: Baseline (single‑stage) → `transfer_learning.enabled: false`
- B: TL enabled → `transfer_learning.enabled: true`

Commands:

```powershell
# A: Baseline
$env:RUN_NAME="baseline"; python run_las_demo.py
# B: TL (after enabling in config)
$env:RUN_NAME="transfer_learning"; python run_las_demo.py
```

Suggested outputs:
- Save metrics into `output/results/{RUN_NAME}_metrics.json`
- Save plots with `{RUN_NAME}_` prefix

Acceptance: Keep TL as default‑off unless it improves RMSE by ≥3–5% or increases R² without added variance across wells.

Estimated time: 2–3 hours (including runs)

---

### 2.7 Documentation Updates



- Document the new `transfer_learning` config block in `README.md`
- Provide example configs for both A and B runs in `configs/`
- Add short notes to `COMPREHENSIVE_PLOTTING_GUIDE.md` if new plots/metrics are added

Estimated time: 30–45 min
[Back to top](#phase-overview-and-navigation)


---

## Validation Checklist (Per Phase)

- Env: `pytest -q` passes; PyTorch import ok
- Baseline demo: exits 0, artifacts created, metrics printed
- Config additions: YAML loads from all entry points
- Transfer learning (examples): two stages run; optional checkpoint saved; no regression when disabled
- Guardrails: `tests/test_stability_lock.py` passes locally and in CI
- Physics metrics: alignment printed and/or saved to JSON
- A/B: artifacts clearly separated; deltas quantified

---

## Troubleshooting Guide

- Symptom: CUDA OOM or no GPU
  - Fix: Reduce batch size in config (e.g., 32 → 16 → 8) or run on CPU (PyTorch auto‑falls back)
- Symptom: Shapes mismatch in GRU forward
  - Fix: Ensure inputs are `(batch, seq, features)`. Verify `sequence_length` and feature concatenations
- Symptom: Physics predictions look unrealistic
  - Fix: Check VP units and order. Denormalize VP to m/s first, then convert to km/s before physics model
- Symptom: No improvement with TL or overfitting in Stage B
  - Fix: Increase `lr_reduction_factor` (smaller LR), enable early stopping, shorten `finetune_epochs`
- Symptom: Import errors
  - Fix: `pip install -r requirements.txt`; ensure `src` is on the Python path where needed
- Symptom: Baseline regressed after changes
  - Fix: Toggle `transfer_learning.enabled: false` to quickly rollback; run guardrail test

---

## Time & Effort Summary

- Setup & baseline: 45–90 min
- Config additions: 10–15 min
- TL in examples: 60–90 min
- Optional TL in LAS pipeline: 60–90 min
- Guardrails: 30–45 min
- Physics metrics: 45–60 min
- A/B runs + reporting: 120–180 min
- Docs: 30–45 min

Total (if all items): ~6–9 hours depending on hardware and data volume.

---

## Appendix: Useful Paths & Commands

- Entry points
  - Synthetic example: `examples/train_model.py`
  - LAS demo: `run_las_demo.py`
  - Config: `configs/default_config.yaml`
  - Trainer: `src/training/trainer.py`
  - Strategies enum: `src/training/strategies.py`
- Tests
  - `pytest -q` (all)
  - `pytest -q tests/test_stability_lock.py`
- Outputs
  - Models: `output/models/`
  - Results: `output/results/`
  - Visualizations: `output/visualizations/`

---



## Phase 3 — Future Enhancements (Build on Phase 2; Stability‑First)

> Summary
> - Objective: Expand capabilities with pluggable physics, default pseudolabels, a dedicated TL trainer, robust evaluation, and experiments
> - Dependencies: Phase 2
> - Outputs: Plugin registry, pseudolabels pathway, TransferLearningTrainer, BlindTestEvaluator, experiment runner, extended CI
> - Time: ~3-4 weeks (modular; proceed incrementally)


Phase 3 consolidates advanced capabilities from the comprehensive plan into a sequenced roadmap. Each item preserves the config‑driven, minimal‑risk approach, depends on Phase 2 completion (minimal transfer learning, guardrails, metrics), and maintains rollback via config flags.

### 3.1 — Pluggable Rock Physics Architecture (Multiple Models + Registry)

Objective: Support multiple physics constraints via a simple plugin architecture and factory registry.

Prerequisites:
- Phase 2 completed; baseline stable and TL gated via config
- Unit flow stable (m/s ↔ km/s) and tests passing

Implementation Steps:
1) Add new physics models
   - Create:
     - src/models/rock_physics/empirical_vp_vs.py (fit: VS = a*VP + b)
     - src/models/rock_physics/multiparameter_regression.py (fit: VS = a*GR + b*DEN + c*VP + d*RES + e)
   - Both expose fit(...) and predict(...), units in km/s to match MudrockLine
2) Extend factory/registry
   - Update src/models/rock_physics/__init__.py to register 'empirical_vp_vs' and 'multiparameter_regression'
   - Keep RockPhysicsFactory.register(name, cls) for later external plugins
3) Config surface (backward compatible)
   - rock_physics.model_type: one of [mudrock_line, empirical_vp_vs, multiparameter_regression]
   - rock_physics.params: model‑specific (e.g., none for empirical until fit)
4) Training usage
   - No core trainer changes required if units are respected; models created via factory from config

Validation & Acceptance:
- Unit tests for each model: fit/predict shapes, parameter recovery (toy data)
- Factory creates models by type; default mudrock_line stays unchanged
- LAS demo runs end‑to‑end for all three models without crashes
- Acceptance: No regression with mudrock_line; additional models yield realistic magnitudes/ranges

Troubleshooting:
- Unfitted model errors → ensure empirical/MP models call fit prior to predict where required
- Unit mismatch → confirm VP to km/s conversion before predict

Estimated effort: 2–3 days

---

### 3.2 — Full Pseudolabel Integration as Default Pathway (Config‑Gated)

Objective: Make sequence‑compatible pseudolabels a first‑class pathway; default‑on via config with instant rollback.

Prerequisites:
- 3.1 optional but recommended (consistent units)
- Preprocessor supports inverse/forward transforms and scalers

Implementation Steps:
1) StrategyHandler: sequence pseudolabels
   - Add generate_pseudolabels(features[B,T,F], feature_index/manager) that extracts VP (by name), converts to km/s, predicts VS_phys, returns [B,T,1]
2) Preprocessing: add pseudolabel channel pre‑normalization
   - In data preprocessing, when training.strategy == 'pseudolabels', concatenate VS_phys as an extra feature channel before scaling
3) Model input dim
   - Dynamically set input_dim = base_features + 1 when pseudolabels enabled
4) Config and defaults
   - training.strategy: "pseudolabels" (Phase 3 default)
   - Add training.pseudolabels: { enabled: true } to allow instant rollback by toggling false

Validation & Acceptance:
- Shapes consistent throughout (B,T,F+1)
- End‑to‑end run succeeds; guardrail tests still pass (RMSE ≤ 80 m/s, R² ≥ 0.96)
- Acceptance: Equal or improved validation metrics vs. baseline; no scaling/unit regressions

Troubleshooting:
- Dimension mismatch → verify concatenation before scaler and corresponding input_dim
- Poor metrics → check VP unit denorm and km/s conversion prior to physics predict

Estimated effort: ~1 week (including QA)

---

### 3.3 — Complete TransferLearningTrainer Class

Objective: Replace minimal TL branch with a dedicated trainer class supporting two‑stage routines, checkpoints, and early stopping.

Prerequisites:
- Phase 2 TL variant shows benefit; transfer_learning config present

Implementation Steps:
1) Create src/training/transfer_trainer.py
   - class TransferLearningTrainer(PhysicsGuidedTrainer)
   - pretrain_stage(dataloader, epochs): build VS_phys targets on‑the‑fly and train with MSE
   - finetune_stage(train_loader, val_loader, epochs): reduce LR by factor, early stopping, save best
2) Entry point integration
   - If training.strategy == 'transfer_learning': instantiate TransferLearningTrainer; else use PhysicsGuidedTrainer
3) Config
   - Reuse transfer_learning block (pretrain_epochs, finetune_epochs, pretrain_lr, lr_reduction_factor, patience, save_pretrained_model)

Validation & Acceptance:
- A/B vs. Phase 2 minimal TL shows equal or better metrics
- Acceptance: RMSE improves ≥3–5% or R² increases without added variance; parity when disabled

Troubleshooting:
- Overfitting in finetune → lower LR (increase reduction), tighten patience
- Physics target scale off → recheck VP denorm and km/s path

Estimated effort: 3–5 days

---

### 3.4 — Advanced Evaluation Framework (Blind‑Test, Physical Units)

Objective: Implement BlindTestEvaluator for cross‑well evaluation in physical units, with aggregation.

Prerequisites:
- Reliable inverse transforms to physical units for features and targets
- Access to well‑separated datasets or indices

Implementation Steps:
1) Create src/evaluation/evaluator.py
   - class BlindTestEvaluator with cross_well_evaluation(wells_data, model_class, config)
   - Evaluate in km/s; aggregate mean/std RMSE, MAE, correlation across train/test wells
2) Integrate into demo/experiments
   - Optionally add CLI or a demo script to run the protocol and save JSON results

Validation & Acceptance:
- Produces aggregated metrics and detailed per‑well results; artifacts saved under output/ or experiments/
- Acceptance: Reproducible runs with identical configs yield consistent aggregates

Troubleshooting:
- Inconsistent units → verify physical unit conversions before metrics
- Empty wells_data → validate LAS parsing and well separation

Estimated effort: 3–4 days

---

### 3.5 — Configuration‑Driven Feature Management & Validation

Objective: Improve robustness by replacing implicit indices with explicit feature management and config validation.

Prerequisites:
- None (can be done in parallel), but integrate after 3.1 for unit consistency

Implementation Steps:
1) Create src/utils/feature_manager.py
   - Resolve indices by feature name, manage units, assist (de)normalization
2) Create src/utils/config_validator.py
   - Validate names/indices/unit mappings; ensure vp_channel exists
3) Integrate (gated)
   - Optionally gate with feature_management.enabled to minimize churn
   - Gradually update trainer paths to use FeatureManager

Validation & Acceptance:
- Unit tests: index lookup, unit conversion, denorm path
- Acceptance: No behavior change when gate is off; equivalent behavior when on

Troubleshooting:
- Validation failures → fix config structure (names/indices mismatch)

Estimated effort: 2–3 days

---

### 3.6 — Experiment Orchestration Framework

Objective: Systematic grid of constraints × strategies with result tracking.

Prerequisites:
- 3.1–3.4 implemented; evaluator available

Implementation Steps:
1) Create src/experiments/experiment_runner.py
   - Run grid over constraints ['mudrock_line','empirical_vp_vs','multiparameter_regression'] × strategies ['loss_function','pseudolabels','transfer_learning']
   - Save each run’s results to experiments/{constraint}_{strategy}/results.json
2) CLI or script
   - Provide entry for full grid or subsets via config

Validation & Acceptance:
- All combinations run without errors; JSONs saved; aggregated report produced

Troubleshooting:
- Long runtimes → reduce epochs/batch or subset grid

Estimated effort: 2–3 days

---

### 3.7 — Guardrails, Rollback, and CI

- Extend tests: add unit tests for new physics models and feature manager
- Keep config gates for pseudolabels/transfer_learning/feature_management; defaults should preserve current behavior
- CI: run quick smoke (pytest -q) and selected evaluation to catch regressions

Estimated effort: 1–2 days

---

### Phase 3 Overall Acceptance & Timeline

- All subsections validated individually; end‑to‑end pipeline stable
- No regression to Phase 2 guardrails (RMSE ≤ 80 m/s, R² ≥ 0.96) when advanced features are disabled
- Recommended sequence & time (conservative):
  - 3.1 Pluggable physics: 2–3 days



  - 3.2 Pseudolabels default (gated): ~5 days
  - 3.3 TL trainer class: 3–5 days
  - 3.4 Advanced evaluation: 3–4 days
  - 3.5 Feature mgmt & validation: 2–3 days
  - 3.6 Orchestration: 2–3 days
  - 3.7 Guardrails/CI: 1–2 days

Total Phase 3: ~3–4 weeks depending on hardware and data volume.

[Back to top](#phase-overview-and-navigation)

