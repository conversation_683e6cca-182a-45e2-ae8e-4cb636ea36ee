import pytest
import numpy as np
from src.models.rock_physics import RockPhysicsFactory
from src.models.rock_physics.empirical_vpvs import EmpiricalVPVS
from src.models.rock_physics.multiparameter import MultiparameterRegression

class TestEmpiricalVPVSModel:
    """Test suite for the EmpiricalVPVS rock physics model."""
    
    def test_default_initialization(self):
        """Test model initialization with default parameters."""
        model = EmpiricalVPVS()
        
        assert model.name == "empirical_vpvs"
        assert model.a == 0.8619  # Default from paper
        assert model.b == 0.8621  # Default from paper
        assert model.fitted == True  # Uses default coefficients
    
    def test_custom_initialization(self):
        """Test model initialization with custom parameters."""
        custom_a, custom_b = 0.9, 0.85
        model = EmpiricalVPVS(a=custom_a, b=custom_b)
        
        assert model.a == custom_a
        assert model.b == custom_b
        assert model.fitted == True
    
    def test_predict_with_array_input(self):
        """Test prediction with numpy array input."""
        model = EmpiricalVPVS()
        vp_values = np.array([3.0, 4.0, 5.0])  # km/s
        
        vs_pred = model.predict(vp_values)
        
        # Expected: VS = a * VP^b
        expected = model.a * np.power(vp_values, model.b)
        np.testing.assert_allclose(vs_pred, expected, rtol=1e-10)
        
        # Basic sanity checks
        assert vs_pred.shape == vp_values.shape
        assert np.all(vs_pred > 0)  # Positive velocities
        assert np.all(vs_pred < vp_values)  # VS should be < VP for typical rocks
    
    def test_predict_with_dict_input(self):
        """Test prediction with dictionary input."""
        model = EmpiricalVPVS()
        vp_values = np.array([3.0, 4.0, 5.0])  # km/s
        features = {'VP': vp_values}
        
        vs_pred = model.predict(features)
        
        # Should give same result as array input
        vs_array = model.predict(vp_values)
        np.testing.assert_allclose(vs_pred, vs_array, rtol=1e-10)
    
    def test_predict_error_handling(self):
        """Test error handling in predict method."""
        model = EmpiricalVPVS()
        
        # Test missing VP key in dict
        with pytest.raises(ValueError, match="Dictionary input must contain 'VP' key"):
            model.predict({'GR': np.array([50, 60, 70])})
        
        # Test invalid input type
        with pytest.raises(TypeError):
            model.predict("invalid_input")
    
    def test_fit_from_data(self):
        """Test fitting model parameters from synthetic data."""
        # Create synthetic data with known relationship
        true_a, true_b = 0.8, 0.9
        vp_train = np.array([2.5, 3.0, 3.5, 4.0, 4.5, 5.0])
        vs_train = true_a * np.power(vp_train, true_b)
        
        # Add small amount of noise
        np.random.seed(42)
        vs_train += np.random.normal(0, 0.01, len(vs_train))
        
        model = EmpiricalVPVS()
        model.fit(vp_train, vs_train)
        
        # Check that fitted parameters are close to true values
        assert abs(model.a - true_a) < 0.05
        assert abs(model.b - true_b) < 0.05
        assert model.fitted == True
        assert hasattr(model, 'fit_rmse')
    
    def test_fit_with_dict_input(self):
        """Test fitting with dictionary input."""
        vp_train = np.array([3.0, 4.0, 5.0])
        vs_train = np.array([1.8, 2.3, 2.8])
        features = {'VP': vp_train}
        
        model = EmpiricalVPVS()
        original_params = (model.a, model.b)
        
        model.fit(features, vs_train)
        
        # Parameters should have changed
        assert (model.a, model.b) != original_params
        assert model.fitted == True
    
    def test_get_equation(self):
        """Test equation string generation."""
        model = EmpiricalVPVS(a=0.8619, b=0.8621)
        equation = model.get_equation()
        
        expected = "VS = 0.8619 × VP^0.8621"
        assert equation == expected
    
    def test_get_fit_quality(self):
        """Test fit quality metrics."""
        model = EmpiricalVPVS()
        
        # Before fitting
        metrics = model.get_fit_quality()
        assert metrics['fitted'] == True  # Default coefficients
        assert 'rmse_log_space' not in metrics  # No fit RMSE yet
        
        # After fitting
        vp = np.array([3.0, 4.0, 5.0])
        vs = np.array([1.8, 2.3, 2.8])
        model.fit(vp, vs)
        
        metrics = model.get_fit_quality()
        assert metrics['fitted'] == True
        assert 'rmse_log_space' in metrics
        assert isinstance(metrics['rmse_log_space'], float)


class TestMultiparameterRegression:
    """Test suite for the MultiparameterRegression rock physics model."""
    
    def test_default_initialization(self):
        """Test model initialization with default parameters."""
        model = MultiparameterRegression()
        
        assert model.name == "multiparameter"
        assert model.feature_names == ['GR', 'DEN', 'VP', 'RES']
        assert 'GR' in model.coefficients
        assert 'intercept' in model.coefficients
        assert model.fitted == False  # Default coefficients need fitting
    
    def test_custom_initialization(self):
        """Test model initialization with custom coefficients."""
        custom_coeffs = {
            'GR': 0.001, 'DEN': 0.4, 'VP': 0.35, 'RES': -0.002, 'intercept': 1.1
        }
        model = MultiparameterRegression(coefficients=custom_coeffs)
        
        assert model.coefficients == custom_coeffs
        assert model.fitted == True  # Custom coefficients provided
    
    def test_predict_with_dict_input(self):
        """Test prediction with dictionary input."""
        model = MultiparameterRegression()
        
        features = {
            'GR': np.array([40, 60, 80]),     # API
            'DEN': np.array([2.2, 2.4, 2.6]), # g/cm³
            'VP': np.array([3.0, 4.0, 5.0]),  # km/s
            'RES': np.array([10, 100, 1000])  # ohm.m
        }
        
        vs_pred = model.predict(features)
        
        assert vs_pred.shape == (3,)
        assert np.all(vs_pred > 0)  # Positive velocities
        assert np.all(np.isfinite(vs_pred))  # No NaN or inf values
    
    def test_predict_with_array_input(self):
        """Test prediction with numpy array input."""
        model = MultiparameterRegression()
        
        # Features in order: [GR, DEN, VP, RES]
        features_array = np.array([
            [40, 2.2, 3.0, 10],    # Sample 1
            [60, 2.4, 4.0, 100],   # Sample 2
            [80, 2.6, 5.0, 1000]   # Sample 3
        ])
        
        vs_pred = model.predict(features_array)
        
        assert vs_pred.shape == (3,)
        assert np.all(vs_pred > 0)
        assert np.all(np.isfinite(vs_pred))
        
        # Test single sample (1D input)
        single_sample = features_array[0]  # 1D array
        vs_single = model.predict(single_sample)
        assert isinstance(vs_single, (float, np.floating))
        assert vs_single == vs_pred[0]
    
    def test_predict_consistency(self):
        """Test that dict and array inputs give same results."""
        model = MultiparameterRegression()
        
        # Same data in both formats
        features_dict = {
            'GR': np.array([50, 70]),
            'DEN': np.array([2.3, 2.5]),
            'VP': np.array([3.5, 4.5]),
            'RES': np.array([50, 200])
        }
        
        features_array = np.array([
            [50, 2.3, 3.5, 50],
            [70, 2.5, 4.5, 200]
        ])
        
        vs_dict = model.predict(features_dict)
        vs_array = model.predict(features_array)
        
        np.testing.assert_allclose(vs_dict, vs_array, rtol=1e-10)
    
    def test_predict_error_handling(self):
        """Test error handling in predict method."""
        model = MultiparameterRegression()
        
        # Missing required features
        incomplete_features = {'GR': np.array([50]), 'VP': np.array([3.0])}
        with pytest.raises(ValueError, match="Missing required features"):
            model.predict(incomplete_features)
        
        # Wrong array dimensions
        wrong_shape = np.array([[1, 2, 3]])  # Only 3 columns, need 4
        with pytest.raises(ValueError, match="must have 4 columns"):
            model.predict(wrong_shape)
        
        # Invalid input type
        with pytest.raises(TypeError):
            model.predict("invalid")
    
    def test_resistivity_log_transform(self):
        """Test that resistivity is properly log-transformed."""
        model = MultiparameterRegression(coefficients={
            'GR': 0, 'DEN': 0, 'VP': 0, 'RES': 1.0, 'intercept': 0
        })
        
        # Only RES coefficient is non-zero, so VS should equal log10(RES)
        features = {
            'GR': np.array([0]),
            'DEN': np.array([0]),
            'VP': np.array([0]),
            'RES': np.array([100])  # log10(100) = 2
        }
        
        vs_pred = model.predict(features)
        expected = np.log10(100)  # Should be 2.0
        
        np.testing.assert_allclose(vs_pred, expected, rtol=1e-10)
    
    def test_fit_functionality(self):
        """Test model fitting with synthetic data."""
        # Create synthetic multiparameter data
        n_samples = 100
        np.random.seed(42)
        
        gr = np.random.uniform(20, 100, n_samples)      # API
        den = np.random.uniform(2.0, 2.8, n_samples)   # g/cm³
        vp = np.random.uniform(2.5, 5.5, n_samples)    # km/s
        res = np.random.uniform(1, 1000, n_samples)    # ohm.m
        
        # Create synthetic VS using known coefficients
        true_coeffs = {'GR': 0.001, 'DEN': 0.6, 'VP': 0.4, 'RES': -0.0005, 'intercept': 0.8}
        vs_true = (true_coeffs['GR'] * gr + 
                   true_coeffs['DEN'] * den + 
                   true_coeffs['VP'] * vp + 
                   true_coeffs['RES'] * np.log10(res) + 
                   true_coeffs['intercept'])
        
        # Add noise
        vs_true += np.random.normal(0, 0.05, n_samples)
        
        # Fit model
        features = {'GR': gr, 'DEN': den, 'VP': vp, 'RES': res}
        model = MultiparameterRegression()
        model.fit(features, vs_true)
        
        # Check that fitted coefficients are reasonable
        for feature in ['GR', 'DEN', 'VP', 'RES']:
            fitted_coeff = model.coefficients[feature]
            true_coeff = true_coeffs[feature]
            # Allow some tolerance due to noise
            assert abs(fitted_coeff - true_coeff) < 0.1, f"Feature {feature}: fitted={fitted_coeff}, true={true_coeff}"
        
        # Check fit quality
        assert model.fitted == True
        assert hasattr(model, 'fit_rmse')
        assert hasattr(model, 'fit_r2')
        assert model.fit_r2 > 0.8  # Should have good fit
    
    def test_get_equation(self):
        """Test equation string generation."""
        coeffs = {'GR': 0.001, 'DEN': 0.5, 'VP': 0.3, 'RES': -0.001, 'intercept': 1.0}
        model = MultiparameterRegression(coefficients=coeffs)
        
        equation = model.get_equation()
        
        # Check that all components are in the equation
        assert "GR" in equation
        assert "DEN" in equation
        assert "VP" in equation
        assert "log10(RES)" in equation  # RES should be log-transformed
        assert "1.0000" in equation  # Intercept
    
    def test_get_feature_importance(self):
        """Test feature importance calculation."""
        coeffs = {'GR': 0.1, 'DEN': -0.3, 'VP': 0.5, 'RES': -0.2, 'intercept': 1.0}
        model = MultiparameterRegression(coefficients=coeffs)
        
        importance = model.get_feature_importance()
        
        # Should return absolute values
        assert importance['GR'] == 0.1
        assert importance['DEN'] == 0.3  # abs(-0.3)
        assert importance['VP'] == 0.5
        assert importance['RES'] == 0.2  # abs(-0.2)
        assert 'intercept' not in importance  # Intercept not included


class TestRockPhysicsFactory:
    """Test suite for the RockPhysicsFactory with new models."""
    
    def test_factory_registration(self):
        """Test that new models are properly registered in factory."""
        available_models = RockPhysicsFactory._models.keys()
        
        assert "mudrock_line" in available_models
        assert "empirical_vpvs" in available_models
        assert "multiparameter" in available_models
    
    def test_create_empirical_vpvs(self):
        """Test creating EmpiricalVPVS through factory."""
        model = RockPhysicsFactory.create("empirical_vpvs")
        
        assert isinstance(model, EmpiricalVPVS)
        assert model.name == "empirical_vpvs"
    
    def test_create_multiparameter(self):
        """Test creating MultiparameterRegression through factory."""
        model = RockPhysicsFactory.create("multiparameter")
        
        assert isinstance(model, MultiparameterRegression)
        assert model.name == "multiparameter"
    
    def test_create_with_custom_parameters(self):
        """Test creating models with custom parameters through factory."""
        # EmpiricalVPVS with custom parameters
        model1 = RockPhysicsFactory.create("empirical_vpvs", a=0.9, b=0.85)
        assert model1.a == 0.9
        assert model1.b == 0.85
        
        # MultiparameterRegression with custom coefficients
        custom_coeffs = {'GR': 0.002, 'DEN': 0.4, 'VP': 0.35, 'RES': -0.003, 'intercept': 0.9}
        model2 = RockPhysicsFactory.create("multiparameter", coefficients=custom_coeffs)
        assert model2.coefficients == custom_coeffs
    
    def test_create_unknown_model(self):
        """Test error handling for unknown model types."""
        with pytest.raises(ValueError, match="Unknown model type"):
            RockPhysicsFactory.create("nonexistent_model")


class TestModelInteroperability:
    """Test that all models work together consistently."""
    
    def test_all_models_basic_functionality(self):
        """Test that all registered models have basic functionality."""
        model_names = ["mudrock_line", "empirical_vpvs", "multiparameter"]
        
        for model_name in model_names:
            model = RockPhysicsFactory.create(model_name)
            
            # Check basic attributes
            assert hasattr(model, 'name')
            assert hasattr(model, 'predict')
            assert hasattr(model, 'fit')
            assert hasattr(model, 'fitted')
            
            # Check that predict method exists and is callable
            assert callable(model.predict)
            assert callable(model.fit)
    
    def test_vp_only_models_compatibility(self):
        """Test that VP-only models work with both input formats."""
        vp_models = ["mudrock_line", "empirical_vpvs"]
        vp_values = np.array([3.0, 4.0, 5.0])
        
        for model_name in vp_models:
            model = RockPhysicsFactory.create(model_name)
            
            # Test array input (backward compatibility)
            vs1 = model.predict(vp_values)
            
            # Test dict input (new interface)
            vs2 = model.predict({'VP': vp_values})
            
            # Should give same results
            np.testing.assert_allclose(vs1, vs2, rtol=1e-10)
            
            # Basic sanity checks
            assert len(vs1) == len(vp_values)
            assert np.all(vs1 > 0)
            assert np.all(vs1 < vp_values)  # VS < VP for typical rocks
    
    def test_error_handling_consistency(self):
        """Test that all models handle errors consistently."""
        model_names = ["mudrock_line", "empirical_vpvs", "multiparameter"]
        
        for model_name in model_names:
            model = RockPhysicsFactory.create(model_name)
            
            # Test invalid input type
            with pytest.raises((TypeError, ValueError)):
                model.predict("invalid_input")
            
            # Test empty input handling
            try:
                result = model.predict(np.array([]))
                # If no error, verify the result makes sense
                assert len(result) == 0 or np.isscalar(result), "Empty input should return empty or scalar result"
            except (ValueError, IndexError, TypeError):
                # Expected behavior for some models
                pass


if __name__ == "__main__":
    # Run tests if script is executed directly
    pytest.main([__file__, "-v"])