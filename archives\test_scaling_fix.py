#!/usr/bin/env python3
"""
Test script to verify that the scaling/magnitude issues are resolved.
This script creates a minimal training run and checks the output scaling.
"""
import sys
import os
import yaml
import torch
import numpy as np
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader, TensorDataset

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from models.neural_networks import BiGRU
from models.rock_physics import RockPhysicsFactory
from training.strategies import PhysicsGuidanceStrategy, StrategyHandler
from training.trainer import PhysicsGuidedTrainer
from data.preprocessing import WellLogPreprocessor

def test_scaling_fix():
    """Test that the scaling issues are resolved."""
    print("Testing Scaling Fix: BiGRU vs Physics Model vs Actual Data")
    print("=" * 70)
    
    # Load config
    config_path = "configs/default_config.yaml"
    with open(config_path, "r") as f:
        config = yaml.safe_load(f)
    
    # Create synthetic realistic data
    n_samples = 100
    seq_len = 50
    n_features = len(config['data']['features'])
    
    # Create realistic well log data (in physical units)
    np.random.seed(42)
    
    # P-WAVE: 2000-6000 m/s
    p_wave = np.random.uniform(2000, 6000, (n_samples, seq_len))
    
    # RHOB: 1.8-2.8 g/cm³
    rhob = np.random.uniform(1.8, 2.8, (n_samples, seq_len))
    
    # PHIE: 0.05-0.35 (porosity)
    phie = np.random.uniform(0.05, 0.35, (n_samples, seq_len))
    
    # RT: 0.1-1000 ohm-m (will be log transformed)
    rt = np.random.lognormal(1, 2, (n_samples, seq_len))
    rt = np.clip(rt, 0.1, 1000)
    
    # S-WAVE: Use mudrock line as ground truth (with noise)
    # Convert P-wave to km/s for mudrock line
    p_wave_kms = p_wave / 1000.0
    s_wave_kms = (p_wave_kms - 1.36) / 1.16  # Mudrock line: VS = (VP - b) / a
    # Add realistic noise
    s_wave_kms += np.random.normal(0, 0.1, s_wave_kms.shape)
    s_wave_kms = np.clip(s_wave_kms, 0.5, 4.0)  # Realistic S-wave range
    s_wave = s_wave_kms * 1000.0  # Convert back to m/s
    
    # Combine features
    features = np.stack([p_wave, rhob, phie, rt], axis=-1)
    targets = s_wave[..., np.newaxis]
    
    print(f"✓ Created realistic synthetic data:")
    print(f"  Features shape: {features.shape}")
    print(f"  P-WAVE range: {p_wave.min():.0f}-{p_wave.max():.0f} m/s")
    print(f"  S-WAVE range: {s_wave.min():.0f}-{s_wave.max():.0f} m/s")
    print(f"  RHOB range: {rhob.min():.2f}-{rhob.max():.2f} g/cm³")
    print(f"  RT range: {rt.min():.2f}-{rt.max():.2f} ohm-m")
    
    # Preprocess data
    preprocessor = WellLogPreprocessor(
        normalization=config['data']['normalization'],
        feature_engineering=True
    )

    features_norm, targets_norm = preprocessor.fit_transform(
        features.reshape(-1, n_features),
        targets.reshape(-1, 1),
        feature_names=config['data']['features'],
        target_names=config['data']['target']
    )

    # Reshape back to sequences
    features_norm = features_norm.reshape(n_samples, seq_len, -1)
    targets_norm = targets_norm.reshape(n_samples, seq_len, 1)

    print(f"✓ Data preprocessed:")
    print(f"  Normalized features shape: {features_norm.shape}")
    print(f"  Normalized targets shape: {targets_norm.shape}")

    # Update model input dimension based on preprocessed features
    actual_input_dim = features_norm.shape[-1]
    config['model']['params']['input_dim'] = actual_input_dim

    # Create models
    rock_physics_model = RockPhysicsFactory.create(
        config['rock_physics']['model_type'],
        **config['rock_physics']['params']
    )

    strategy = PhysicsGuidanceStrategy(config['training']['strategy'])
    strategy_handler = StrategyHandler(strategy, rock_physics_model)

    model = BiGRU(**config['model']['params'])

    print(f"✓ Model created with input_dim: {actual_input_dim}")
    
    # Create trainer with config
    trainer = PhysicsGuidedTrainer(
        model=model,
        rock_physics_model=rock_physics_model,
        strategy_handler=strategy_handler,
        config=config
    )
    
    # Convert to tensors
    features_tensor = torch.FloatTensor(features_norm)
    targets_tensor = torch.FloatTensor(targets_norm)
    
    # Quick training (just a few steps to get some predictions)
    dataset = TensorDataset(features_tensor, targets_tensor)
    dataloader = DataLoader(dataset, batch_size=10, shuffle=True)
    
    optimizer = torch.optim.Adam(model.parameters(), lr=0.01)
    loss_fn = strategy_handler.get_loss_function()
    
    print("✓ Running quick training (5 epochs)...")
    for epoch in range(5):
        train_loss = trainer.train_epoch(dataloader, optimizer, loss_fn)
        if epoch % 2 == 0:
            print(f"  Epoch {epoch}: Loss = {train_loss:.4f}")
    
    # Get predictions
    model.eval()
    with torch.no_grad():
        test_features = features_tensor[:10].to(trainer.device)  # First 10 samples
        predictions_norm = model(test_features)
    
    # Denormalize predictions and targets for comparison
    predictions_phys = preprocessor.scalers['targets'].inverse_transform(
        predictions_norm.cpu().numpy().reshape(-1, 1)
    ).reshape(predictions_norm.shape)
    
    targets_phys = preprocessor.scalers['targets'].inverse_transform(
        targets_tensor[:10].cpu().numpy().reshape(-1, 1)
    ).reshape(targets_tensor[:10].shape)
    
    # Get physics predictions (using proper unit conversion)
    vp_norm = features_tensor[:10, :, trainer.vp_index]
    conversion_factor = config["physics_coupling"].get("conversion_factor", 0.001)
    vp_physics = vp_norm * conversion_factor  # This should be wrong - we need to denormalize first
    
    # Correct approach: denormalize VP first, then convert units
    vp_phys_ms = preprocessor.scalers['features'].inverse_transform(
        features_tensor[:10, :, :len(config['data']['features'])].cpu().numpy().reshape(-1, len(config['data']['features']))
    )[:, trainer.vp_index].reshape(10, seq_len)
    
    vp_phys_kms = vp_phys_ms / 1000.0  # Convert m/s to km/s
    
    physics_predictions_kms = np.array([
        rock_physics_model.predict(vp_phys_kms[i]) for i in range(10)
    ])
    physics_predictions_ms = physics_predictions_kms * 1000.0  # Convert back to m/s
    
    print(f"✓ Generated predictions:")
    print(f"  BiGRU predictions range: {predictions_phys.min():.0f}-{predictions_phys.max():.0f} m/s")
    print(f"  Physics predictions range: {physics_predictions_ms.min():.0f}-{physics_predictions_ms.max():.0f} m/s")
    print(f"  Actual targets range: {targets_phys.min():.0f}-{targets_phys.max():.0f} m/s")
    
    # Check if scaling is consistent
    bigru_scale = np.mean(np.abs(predictions_phys))
    physics_scale = np.mean(np.abs(physics_predictions_ms))
    actual_scale = np.mean(np.abs(targets_phys))
    
    print(f"\n✓ Scale Analysis:")
    print(f"  BiGRU average magnitude: {bigru_scale:.0f} m/s")
    print(f"  Physics average magnitude: {physics_scale:.0f} m/s")
    print(f"  Actual average magnitude: {actual_scale:.0f} m/s")
    
    # Check if they're in the same order of magnitude
    scale_ratio_bigru = bigru_scale / actual_scale
    scale_ratio_physics = physics_scale / actual_scale
    
    print(f"  BiGRU/Actual ratio: {scale_ratio_bigru:.2f}")
    print(f"  Physics/Actual ratio: {scale_ratio_physics:.2f}")
    
    # Test results
    scaling_fixed = (0.1 < scale_ratio_bigru < 10.0) and (0.1 < scale_ratio_physics < 10.0)
    
    print(f"\n{'='*70}")
    print("SCALING FIX TEST RESULTS:")
    print(f"✓ Unit conversion implemented: {config['physics_coupling']}")
    print(f"✓ VP index resolution: Feature '{config['data']['vp_feature_name']}' at index {trainer.vp_index}")
    print(f"✓ Log transforms applied: {config['data']['apply_log_transform']}")
    print(f"✓ Scaling consistency: {'PASS' if scaling_fixed else 'FAIL'}")
    
    if scaling_fixed:
        print("\n🎉 SUCCESS: Scaling issues have been resolved!")
        print("   - BiGRU predictions are in the same order of magnitude as actual data")
        print("   - Physics model predictions are properly scaled")
        print("   - Unit conversion pipeline is working correctly")
    else:
        print("\n❌ WARNING: Scaling issues may still exist")
        print("   - Check denormalization pipeline")
        print("   - Verify unit conversion factors")
    
    return scaling_fixed

if __name__ == "__main__":
    try:
        success = test_scaling_fix()
        if success:
            print("\n✅ Phase 1 scaling fix verification PASSED!")
        else:
            print("\n⚠️  Phase 1 scaling fix verification needs attention")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
