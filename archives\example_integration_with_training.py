#!/usr/bin/env python3
"""
Example showing how to integrate the new rock physics models with the existing training pipeline.

This demonstrates how the models work as standalone functions while being compatible
with the existing BiGRU training infrastructure.
"""

import sys
import os
import numpy as np

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from models.rock_physics import RockPhysicsFactory


def demonstrate_model_selection():
    """Demonstrate how to select appropriate rock physics model based on available data."""
    print("=" * 60)
    print("ROCK PHYSICS MODEL SELECTION GUIDE")
    print("=" * 60)
    
    # Simulate different data availability scenarios
    scenarios = [
        {
            'name': 'VP Only Available',
            'available_features': ['VP'],
            'recommended_models': ['mudrock_line', 'empirical_vpvs'],
            'sample_data': {'VP': np.array([3.0, 4.0, 5.0])}
        },
        {
            'name': 'Full Log Suite Available', 
            'available_features': ['VP', 'GR', 'DEN', 'RES'],
            'recommended_models': ['multiparameter'],
            'sample_data': {
                'VP': np.array([3.0, 4.0, 5.0]),
                'GR': np.array([50, 70, 90]),
                'DEN': np.array([2.2, 2.4, 2.6]),
                'RES': np.array([20, 100, 500])
            }
        }
    ]
    
    for scenario in scenarios:
        print(f"\nScenario: {scenario['name']}")
        print(f"Available features: {scenario['available_features']}")
        print(f"Recommended models: {scenario['recommended_models']}")
        
        # Test each recommended model
        for model_name in scenario['recommended_models']:
            model = RockPhysicsFactory.create(model_name)
            
            if model_name == 'multiparameter':
                vs_pred = model.predict(scenario['sample_data'])
            else:
                # VP-only models can use either array or dict input
                vs_pred = model.predict(scenario['sample_data']['VP'])
            
            print(f"  {model_name}: VS predictions = {vs_pred}")


def demonstrate_standalone_functions():
    """Show how models can be used as standalone functions."""
    print("\n" + "=" * 60)
    print("STANDALONE FUNCTION EXAMPLES")
    print("=" * 60)
    
    # Example 1: Quick VP-VS conversion function
    def convert_vp_to_vs(vp_values, method='empirical_vpvs'):
        """Standalone VP-VS conversion function."""
        model = RockPhysicsFactory.create(method)
        return model.predict(vp_values)
    
    # Example 2: Multi-well batch processing
    def process_multiple_wells(wells_data, model_type='empirical_vpvs'):
        """Process multiple wells with same model."""
        results = {}
        model = RockPhysicsFactory.create(model_type)
        
        for well_name, well_data in wells_data.items():
            vs_pred = model.predict(well_data)
            results[well_name] = vs_pred
        
        return results
    
    # Example 3: Model comparison function
    def compare_all_models(vp_values):
        """Compare predictions from all available models."""
        models = ['mudrock_line', 'empirical_vpvs']
        results = {}
        
        for model_name in models:
            model = RockPhysicsFactory.create(model_name)
            results[model_name] = model.predict(vp_values)
        
        return results
    
    # Test standalone functions
    test_vp = np.array([3.0, 4.0, 5.0])
    
    print("1. VP-VS conversion function:")
    vs_result = convert_vp_to_vs(test_vp)
    print(f"   Input VP:  {test_vp}")
    print(f"   Output VS: {vs_result}")
    
    print("\n2. Multi-well batch processing:")
    wells_data = {
        'Well_A': np.array([3.2, 3.8, 4.4]),
        'Well_B': np.array([2.8, 3.5, 4.2]),
        'Well_C': np.array([3.6, 4.1, 4.8])
    }
    
    well_results = process_multiple_wells(wells_data)
    for well, vs_pred in well_results.items():
        print(f"   {well}: VS = {vs_pred}")
    
    print("\n3. Model comparison:")
    comparison = compare_all_models(test_vp)
    for model_name, predictions in comparison.items():
        print(f"   {model_name}: {predictions}")


def demonstrate_training_integration():
    """Show how models integrate with training infrastructure."""
    print("\n" + "=" * 60)
    print("TRAINING PIPELINE INTEGRATION")
    print("=" * 60)
    
    # Simulate training configuration
    config = {
        'rock_physics': {
            'model_type': 'empirical_vpvs',  # Can be changed easily
            'params': {'a': 0.8619, 'b': 0.8621}
        },
        'data': {
            'features': ['VP', 'GR', 'DEN', 'RES']
        }
    }
    
    print(f"Configuration: {config}")
    
    # Create rock physics model (as done in existing training pipeline)
    rock_physics_model = RockPhysicsFactory.create(
        config['rock_physics']['model_type'],
        **config['rock_physics']['params']
    )
    
    print(f"Created model: {rock_physics_model.name}")
    print(f"Model equation: {rock_physics_model.get_equation()}")
    
    # Simulate training data
    batch_size = 10
    sequence_length = 20
    n_features = len(config['data']['features'])
    
    # Synthetic training batch
    features_batch = np.random.uniform(0, 1, (batch_size, sequence_length, n_features))
    
    # Convert to physical units (simplified)
    vp_physical = features_batch[:, :, 0] * 3.0 + 2.5  # VP in km/s range [2.5, 5.5]
    
    print(f"\nSimulated training batch shape: {features_batch.shape}")
    print(f"VP range: {vp_physical.min():.2f} - {vp_physical.max():.2f} km/s")
    
    # Generate physics predictions (as done in trainer)
    physics_predictions = []
    for i in range(batch_size):
        for j in range(sequence_length):
            vp_val = vp_physical[i, j]
            vs_physics = rock_physics_model.predict(np.array([vp_val]))[0]
            physics_predictions.append(vs_physics)
    
    physics_predictions = np.array(physics_predictions).reshape(batch_size, sequence_length)
    
    print(f"Physics predictions shape: {physics_predictions.shape}")
    print(f"VS range: {physics_predictions.min():.2f} - {physics_predictions.max():.2f} km/s")
    print(f"Average VS/VP ratio: {(physics_predictions.mean() / vp_physical.mean()):.3f}")


def demonstrate_model_fitting():
    """Show how to fit models to well-specific data."""
    print("\n" + "=" * 60)
    print("MODEL FITTING FOR WELL-SPECIFIC CALIBRATION")
    print("=" * 60)
    
    # Simulate well log data with known relationships
    np.random.seed(42)
    n_samples = 50
    
    # Generate synthetic well data
    vp_well = np.random.uniform(2.5, 5.5, n_samples)
    
    # Create "true" VS with some geological relationship + noise
    vs_true_clean = 0.82 * np.power(vp_well, 0.89)  # Similar to empirical but different
    vs_true = vs_true_clean + np.random.normal(0, 0.05, n_samples)
    
    print(f"Well data: {n_samples} samples")
    print(f"VP range: {vp_well.min():.2f} - {vp_well.max():.2f} km/s")
    print(f"True VS range: {vs_true.min():.2f} - {vs_true.max():.2f} km/s")
    
    # Test different models
    models_to_test = ['mudrock_line', 'empirical_vpvs']
    
    for model_name in models_to_test:
        print(f"\n{model_name.upper()} MODEL:")
        
        # Create and test default model
        model = RockPhysicsFactory.create(model_name)
        vs_default = model.predict(vp_well)
        rmse_default = np.sqrt(np.mean((vs_default - vs_true)**2))
        
        print(f"  Default equation: {model.get_equation()}")
        print(f"  Default RMSE: {rmse_default:.4f}")
        
        # Fit model to well data
        model.fit(vp_well, vs_true)
        vs_fitted = model.predict(vp_well)
        rmse_fitted = np.sqrt(np.mean((vs_fitted - vs_true)**2))
        
        print(f"  Fitted equation: {model.get_equation()}")
        print(f"  Fitted RMSE: {rmse_fitted:.4f}")
        print(f"  Improvement: {((rmse_default - rmse_fitted) / rmse_default * 100):.1f}%")


def demonstrate_multiparameter_fitting():
    """Demonstrate multiparameter model fitting."""
    print("\n" + "=" * 60)
    print("MULTIPARAMETER MODEL FITTING")
    print("=" * 60)
    
    # Generate synthetic multiparameter well data
    np.random.seed(42)
    n_samples = 100
    
    well_features = {
        'GR': np.random.uniform(20, 120, n_samples),
        'DEN': np.random.uniform(2.0, 2.8, n_samples),
        'VP': np.random.uniform(2.5, 5.5, n_samples),
        'RES': np.random.uniform(1, 1000, n_samples)
    }
    
    # Create synthetic VS using a known relationship
    true_coeffs = {'GR': 0.0015, 'DEN': 0.7, 'VP': 0.35, 'RES': -0.001, 'intercept': 0.6}
    vs_synthetic = (true_coeffs['GR'] * well_features['GR'] + 
                    true_coeffs['DEN'] * well_features['DEN'] + 
                    true_coeffs['VP'] * well_features['VP'] + 
                    true_coeffs['RES'] * np.log10(well_features['RES']) + 
                    true_coeffs['intercept'])
    vs_synthetic += np.random.normal(0, 0.08, n_samples)
    
    print(f"Synthetic multiparameter data: {n_samples} samples")
    print("True coefficients:", true_coeffs)
    
    # Test default vs fitted model
    model = RockPhysicsFactory.create('multiparameter')
    
    # Default predictions
    vs_default = model.predict(well_features)
    rmse_default = np.sqrt(np.mean((vs_default - vs_synthetic)**2))
    
    print(f"\nDefault model RMSE: {rmse_default:.4f}")
    
    # Fit to well data
    model.fit(well_features, vs_synthetic)
    vs_fitted = model.predict(well_features)
    rmse_fitted = np.sqrt(np.mean((vs_fitted - vs_synthetic)**2))
    
    fit_quality = model.get_fit_quality()
    
    print(f"Fitted model RMSE: {rmse_fitted:.4f}")
    print(f"Fitted model R²: {fit_quality['r2']:.4f}")
    print(f"Improvement: {((rmse_default - rmse_fitted) / rmse_default * 100):.1f}%")
    
    print("\nFitted coefficients:")
    for feature in ['GR', 'DEN', 'VP', 'RES', 'intercept']:
        fitted_val = model.coefficients[feature]
        true_val = true_coeffs[feature]
        error = abs(fitted_val - true_val)
        print(f"  {feature}: fitted={fitted_val:.4f}, true={true_val:.4f}, error={error:.4f}")
    
    # Feature importance
    importance = model.get_feature_importance()
    print(f"\nFeature importance: {importance}")


def main():
    """Main demonstration."""
    print("Rock Physics Models - Training Pipeline Integration Demo")
    print("Based on Zhao et al. (2024) Implementation")
    
    try:
        demonstrate_model_selection()
        demonstrate_standalone_functions()
        demonstrate_training_integration()
        demonstrate_model_fitting()
        demonstrate_multiparameter_fitting()
        
        print("\n" + "=" * 60)
        print("✅ INTEGRATION DEMONSTRATION COMPLETED")
        print("=" * 60)
        
        print("\nKey takeaways:")
        print("1. Models work as standalone functions - can be called independently")
        print("2. Easy integration with existing training pipeline") 
        print("3. Flexible model selection based on available data")
        print("4. Well-specific calibration improves accuracy significantly")
        print("5. Multiparameter models provide comprehensive predictions")
        print("6. Backward compatibility maintained with existing code")
        
        print(f"\nReady for production use!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()