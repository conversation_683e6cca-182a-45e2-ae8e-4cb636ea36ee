# Implement_physics_integration: Aligning Physics-Guided Strategies with the Current Codebase

This document maps the guidance in Guide_physics_integration.md to the current implementation in this repository, with precise references to code constructs and configuration. It clarifies what is already implemented, what is configurable, and what remains as gaps or follow-ups.

## Executive alignment summary

- Physics-guided pseudolabels
  - Implemented two ways:
    - As additional input features in the example pipeline [examples/train_model.py](examples/train_model.py) under the PSEUDOLABELS path.
    - As a reusable strategy utility in [StrategyHandler.prepare_features()](src/training/strategies.py:19) to append physics-based VS to features (expects VP index and 2D arrays).
  - Units: rock-physics models expect VP in km/s; examples feed km/s; StrategyHandler currently does not perform unit conversion.

- Physics-guided loss
  - Fully implemented via [PhysicsGuidedLoss()](src/training/losses.py:5) using the paper’s adaptive formula Loss = lossa + min(lossa, lossb) in [PhysicsGuidedLoss.forward()](src/training/losses.py:15).
  - Integrated in the trainer when strategy is LOSS_FUNCTION: [PhysicsGuidedTrainer.train_epoch()](src/training/trainer.py:34) computes physics_pred consistently with normalization and unit conversion; it passes physics_pred into the combined loss.

- Transfer learning (two-stage)
  - Implemented in [TransferLearningHelper()](src/training/transfer_learning.py:8) with [TransferLearningHelper.pretrain_stage()](src/training/transfer_learning.py:100) on physics-derived targets and [TransferLearningHelper.finetune_stage()](src/training/transfer_learning.py:192) on true targets, including weight save/load and early stopping.
  - Physics-derived targets come from [PhysicsGuidedTrainer.compute_physics_predictions()](src/training/trainer.py:162) with proper denormalization and unit handling.

- Data preparation and normalization
  - Provided by [WellLogPreprocessor()](src/data/preprocessing.py:12) with multiple normalization options and feature engineering.
  - The default config uses standard scaling, not MinMax [-1,1]. MinMax with feature_range [-1,1] is supported and can be enabled via configuration.
  - Log transforms are supported via [apply_log_transforms()](src/data/preprocessing.py:440), and the Multiparameter model internally log-transforms resistivity.

- Evaluation and blind test
  - A comprehensive blind-test protocol aligned with Zhao et al. is provided in [BlindTestProtocol()](src/utils/blind_test_protocol.py:24) including per-well metrics, aggregation, and visualizations.

## Core building blocks in this codebase

- Sequence model
  - [BiGRU()](src/models/neural_networks.py:5): Bidirectional GRU with dropout and linear head.

- Rock-physics models
  - Factory
    - [RockPhysicsFactory()](src/models/rock_physics/__init__.py:6): Creates configured rock-physics models by name.
  - VP-only relationships (VP in km/s, VS returned in km/s)
    - [MudrockLine()](src/models/rock_physics/mudrock_line.py:5) with [MudrockLine.predict()](src/models/rock_physics/mudrock_line.py:17)
    - [EmpiricalVPVS()](src/models/rock_physics/empirical_vpvs.py:5) with [EmpiricalVPVS.predict()](src/models/rock_physics/empirical_vpvs.py:29) using VS = a × VP^b; defaults follow Zhao eq 6.
  - Multiparameter regression (VS from GR, DEN, VP, RES)
    - [MultiparameterRegression()](src/models/rock_physics/multiparameter.py:5) with [MultiparameterRegression.predict()](src/models/rock_physics/multiparameter.py:40); internally applies log10 to resistivity.

- Training and strategies
  - Strategy selection and feature prep
    - [PhysicsGuidanceStrategy()](src/training/strategies.py:6)
    - [StrategyHandler()](src/training/strategies.py:12), [StrategyHandler.prepare_features()](src/training/strategies.py:19), [StrategyHandler.get_loss_function()](src/training/strategies.py:46)
  - Trainer with physics coupling
    - [PhysicsGuidedTrainer()](src/training/trainer.py:8), [PhysicsGuidedTrainer.train_epoch()](src/training/trainer.py:34), [PhysicsGuidedTrainer.evaluate()](src/training/trainer.py:125), [PhysicsGuidedTrainer.compute_physics_predictions()](src/training/trainer.py:162)
  - Two-stage transfer learning
    - [TransferLearningHelper()](src/training/transfer_learning.py:8), [TransferLearningHelper.pretrain_stage()](src/training/transfer_learning.py:100), [TransferLearningHelper.finetune_stage()](src/training/transfer_learning.py:192)

## Mapping the original guide to actual implementation

1) Physics-guided pseudolabels

- Guide intent:
  - Construct pseudolabels from empirical relationships and add as input features to the BiGRU.

- Implemented here:
  - Example pipeline appends physics VS as an extra channel to the input feature tensor when strategy is PSEUDOLABELS: see [examples/train_model.py](examples/train_model.py) around the section beginning at line 49 where features are concatenated and input_dim is incremented.
  - Reusable utility: [StrategyHandler.prepare_features()](src/training/strategies.py:19) appends physics-based VS using a provided vp_index to a 2D feature array.

- Units and normalization:
  - The example path feeds VP directly in km/s into the physics model (consistent and correct).
  - The strategy utility currently does not perform unit conversion or normalization adjustments; if used on normalized inputs, it can mix scales. Prefer to generate pseudolabels either:
    - Before normalization, then scale together with other features via [WellLogPreprocessor.transform_features()](src/data/preprocessing.py:85), or
    - After inverse-transforming VP to physical units via [WellLogPreprocessor.inverse_transform_features()](src/data/preprocessing.py:180).

- Recommendation to standardize
  - For production, prefer performing pseudolabel augmentation as part of a preprocessing step where you control units and scaling, then update model input_dim accordingly. The example demonstrates this pattern clearly.

2) Physics-guided loss function

- Guide intent:
  - Combine conventional data loss (MSE) with physics-consistency penalty: Loss = lossa + min(lossa, lossb).

- Implemented here:
  - [PhysicsGuidedLoss()](src/training/losses.py:5) implements the exact adaptive combination in [PhysicsGuidedLoss.forward()](src/training/losses.py:15).
  - Trainer integration:
    - Computes physics_pred in target space with correct units and scaling in [PhysicsGuidedTrainer.train_epoch()](src/training/trainer.py:34) and passes it into the combined loss when strategy is LOSS_FUNCTION.
    - Physics targets are derived consistently via [PhysicsGuidedTrainer.compute_physics_predictions()](src/training/trainer.py:162), including:
      - VP denormalization from feature scaler if available
      - m/s to km/s conversion via physics_coupling.conversion_factor
      - Model prediction in km/s then converted to m/s
      - Optional re-normalization using target scaler

3) Transfer learning (physics pretrain then fine-tune)

- Guide intent:
  - Pre-train on pseudolabels from empirical models, then fine-tune on true VS with reduced LR; apply across domains as needed.

- Implemented here:
  - Two-stage helper with history and model persistence:
    - Stage-1 pretrain on physics-derived targets: [TransferLearningHelper.pretrain_stage()](src/training/transfer_learning.py:100)
    - Stage-2 fine-tune on true labels: [TransferLearningHelper.finetune_stage()](src/training/transfer_learning.py:192)
    - Pretrain outputs are computed via [PhysicsGuidedTrainer.compute_physics_predictions()](src/training/trainer.py:162) ensuring unit/scale alignment.
  - Learning-rate schedule:
    - pretrain_lr vs finetune_lr configured in config.
  - Weight carryover:
    - Saves pretrained weights and loads them prior to fine-tuning.

- Domain adaptation:
  - Not implemented. The repository does include a robust blind-test protocol for cross-well evaluation [BlindTestProtocol()](src/utils/blind_test_protocol.py:24), but no domain-adaptation loss or adversarial feature alignment is present yet.

## Configuration alignment

- Default config
  - [configs/default_config.yaml](configs/default_config.yaml)
    - Model: BiGRU
    - Rock-physics: mudrock_line
    - Training.strategy: pseudolabels (toggle between pseudolabels, loss_function, transfer_learning)
    - Data normalization: standard by default; MinMax with feature_range [-1,1] is available
    - Physics coupling: vp_units m/s to physics km/s via conversion_factor 0.001
    - Transfer learning parameters provided; disabled by default

- Blind test config (short runs)
  - [configs/blind_test_config.yaml](configs/blind_test_config.yaml)
    - strategy: loss_function for quick evaluation
    - Reduced epochs for faster CI-style checks

- Switching rock-physics models
  - Use the factory [RockPhysicsFactory()](src/models/rock_physics/__init__.py:6) via config rock_physics.model_type with one of:
    - mudrock_line → [MudrockLine()](src/models/rock_physics/mudrock_line.py:5)
    - empirical_vpvs → [EmpiricalVPVS()](src/models/rock_physics/empirical_vpvs.py:5)
    - multiparameter → [MultiparameterRegression()](src/models/rock_physics/multiparameter.py:5)

## Data preparation, units, and scaling

- Preprocessing and scalers
  - [WellLogPreprocessor()](src/data/preprocessing.py:12) supports minmax, standard, robust scaling for both features and targets and handles 3D sequence shapes.

- Unit handling for physics coupling
  - Trainer expects model inputs to be normalized but denormalizes VP before physics prediction:
    - Denormalize VP from feature scaler
    - Convert to km/s before calling physics model
    - Convert physics VS back to m/s and optionally normalize with target scaler
  - Central logic lives in [PhysicsGuidedTrainer.compute_physics_predictions()](src/training/trainer.py:162).

- Log transforms
  - Explicit log10 transforms for designated features at load-time via [apply_log_transforms()](src/data/preprocessing.py:440).
  - The multiparameter model applies log10 to resistivity internally, so do not double-log if you plan to feed RES directly to it.

- Normalization target range
  - The guide suggests (-1,1). Current default is standard scaling for robustness, but MinMax [-1,1] is supported by setting:
    - data.normalization: minmax
    - data.normalization_params.feature_range: [-1, 1]

## Evaluation and blind testing

- Use [BlindTestProtocol()](src/utils/blind_test_protocol.py:24) for:
  - Per-well and aggregated metrics
  - Visualizations:
    - parity, residual distributions, metrics tables, cross-well comparison
  - Outputs are under output_blind_test/blind_test_results/

- Metrics included in trainer eval:
  - [PhysicsGuidedTrainer.evaluate()](src/training/trainer.py:125) computes RMSE and correlation; r2 etc. are covered in the blind test utilities.

## Identified gaps and recommended actions

1) Pseudolabel feature augmentation standardization
- Gap:
  - [StrategyHandler.prepare_features()](src/training/strategies.py:19) assumes 2D feature arrays and does not handle unit conversion. The example path in [examples/train_model.py](examples/train_model.py) correctly handles sequence tensors and input_dim updates, but this is not wired into the core trainer pipeline.
- Recommendation:
  - Promote pseudolabel augmentation to an explicit preprocessing step that:
    - Inverse-transforms VP to km/s via [WellLogPreprocessor.inverse_transform_features()](src/data/preprocessing.py:180) or generates pseudolabels prior to scaling
    - Appends physics VS to features and then re-applies scaling so all channels share the same distribution
    - Updates input_dim consistently in config before model instantiation

2) Multiparameter pseudolabels
- Gap:
  - Pseudolabel generation path commonly uses VP-only models. For [MultiparameterRegression()](src/models/rock_physics/multiparameter.py:5), feature dictionaries or properly ordered arrays are required.
- Recommendation:
  - When using multiparameter as physics teacher, build dict inputs {GR, DEN, VP, RES} in physical units with RES in linear ohm.m (the model will log10 internally), then call [MultiparameterRegression.predict()](src/models/rock_physics/multiparameter.py:40) to generate pseudolabels.

3) Domain adaptation
- Gap:
  - No domain-adaptation loss or adversarial alignment implemented; only two-stage training plus blind testing across wells.
- Recommendation:
  - Roadmap candidates: CORAL penalty, MMD, or domain-adversarial training head. Start by logging per-well feature statistics post-scaling to quantify shift and select a method.

4) Normalization choice
- Gap:
  - Guide suggests (-1,1). Default config uses standard scaling; both are supported.
- Recommendation:
  - Keep standard scaling for stability unless there’s a reason to match paper replication exactly. If reproducing the paper scalings, switch to MinMax with [-1,1] and re-evaluate.

5) Consistency on units
- Gap:
  - Strategy-time pseudolabel augmentation should enforce consistent unit handling like the trainer and TL helper do.
- Recommendation:
  - Centralize unit conversions for physics coupling in a small utility referenced by both trainer and any pseudolabel preprocessing path to avoid duplication and drift.

## Most impactful gaps to fill next

1) Centralize physics unit coupling and scaler-aware conversions
- Why it matters:
  - Unifies how physics-derived signals are produced across pseudolabel augmentation, physics-guided loss, and transfer learning, preventing unit/scale drift and improving stability.
- Action:
  - Extract the conversion and (de)normalization logic from [PhysicsGuidedTrainer.compute_physics_predictions()](src/training/trainer.py:162) and [PhysicsGuidedTrainer.train_epoch()](src/training/trainer.py:34) into a shared utility module [src/utils/physics_coupling.py](src/utils/physics_coupling.py). 
  - Make [StrategyHandler.prepare_features()](src/training/strategies.py:19) call this utility when strategy is PSEUDOLABELS so appended channels match target scale and units.
- Touch points:
  - [PhysicsGuidedTrainer.compute_physics_predictions()](src/training/trainer.py:162), [PhysicsGuidedTrainer.train_epoch()](src/training/trainer.py:34), [StrategyHandler.prepare_features()](src/training/strategies.py:19), [TransferLearningHelper.pretrain_stage()](src/training/transfer_learning.py:100)

2) Sequence-aware pseudolabel augmentation integrated into the core preprocessing path
- Why it matters:
  - Robustly supports 3D [B, T, F] tensors and prevents scale leakage by augmenting before scaling or after inverse-transform in a controlled way.
- Action:
  - Implement augmentation in preprocessing using [WellLogPreprocessor.inverse_transform_features()](src/data/preprocessing.py:180) to convert VP (and other needed features) to physical units, generate physics VS, append as a new channel, then rescale alongside other features.
  - Update configuration in [configs/default_config.yaml](configs/default_config.yaml) to bump model.params.input_dim automatically when the pseudolabel channel is enabled.
- Touch points:
  - [WellLogPreprocessor.inverse_transform_features()](src/data/preprocessing.py:180), [StrategyHandler.prepare_features()](src/training/strategies.py:19), [BiGRU()](src/models/neural_networks.py:5), [examples/train_model.py](examples/train_model.py)

3) Multiparameter physics teacher enablement for pseudolabels and TL
- Why it matters:
  - Teacher signal quality improves by leveraging GR, DEN, VP, RES via the physically motivated regression, especially in heterogeneous formations.
- Action:
  - In [PhysicsGuidedTrainer.compute_physics_predictions()](src/training/trainer.py:162), when the active rock-physics model is multiparameter, inverse-transform the required features via [WellLogPreprocessor.inverse_transform_features()](src/data/preprocessing.py:180), build {GR, DEN, VP, RES} in physical units, and pass to [MultiparameterRegression.predict()](src/models/rock_physics/multiparameter.py:40). Ensure RES is linear ohm.m (internal log10 is already applied by the model).
- Touch points:
  - [MultiparameterRegression.predict()](src/models/rock_physics/multiparameter.py:40), [PhysicsGuidedTrainer.compute_physics_predictions()](src/training/trainer.py:162), [configs/default_config.yaml](configs/default_config.yaml)

4) Optional domain adaptation for cross-well generalization
- Why it matters:
  - Addresses covariate shift across fields/wells beyond physics pretraining, improving blind-test robustness.
- Action:
  - Add a lightweight domain-adaptation module [src/training/domain_adaptation.py](src/training/domain_adaptation.py) with CORAL/MMD penalties, and integrate an optional term into [PhysicsGuidedTrainer.train_epoch()](src/training/trainer.py:34) controlled by configuration weights. Extend blind-test reporting to include domain-shift diagnostics.
- Touch points:
  - [PhysicsGuidedTrainer.train_epoch()](src/training/trainer.py:34), [src/utils/blind_test_protocol.py](src/utils/blind_test_protocol.py), [configs/default_config.yaml](configs/default_config.yaml)
## How to enable each strategy

- Pseudolabels as features
  - Set training.strategy: pseudolabels in [configs/default_config.yaml](configs/default_config.yaml).
  - Follow the example augmentation in [examples/train_model.py](examples/train_model.py) to concatenate physics VS as an extra channel and increment model.params.input_dim accordingly.

- Physics-guided loss
  - Set training.strategy: loss_function. The trainer will:
    - Generate physics_pred with proper denorm and conversions via [PhysicsGuidedTrainer.train_epoch()](src/training/trainer.py:34)
    - Use [PhysicsGuidedLoss()](src/training/losses.py:5) through [StrategyHandler.get_loss_function()](src/training/strategies.py:46)

- Transfer learning
  - Set transfer_learning.enabled: true, and keep training.strategy matching your fine-tuning plan. The example driver in [examples/train_model.py](examples/train_model.py) invokes [TransferLearningHelper()](src/training/transfer_learning.py:8), which runs:
    - [TransferLearningHelper.pretrain_stage()](src/training/transfer_learning.py:100) on physics targets
    - [TransferLearningHelper.finetune_stage()](src/training/transfer_learning.py:192) on true targets

- Switching rock physics models for any strategy
  - Configure rock_physics.model_type in [configs/default_config.yaml](configs/default_config.yaml) to mudrock_line, empirical_vpvs, or multiparameter via the [RockPhysicsFactory()](src/models/rock_physics/__init__.py:6).

## Quick run references

- Example training driver with strategy toggle
  - [examples/train_model.py](examples/train_model.py)

- Tests validating the training loop and losses
  - [tests/test_training.py](tests/test_training.py)

- Tests validating rock-physics models and factory
  - [tests/test_new_rock_physics_models.py](tests/test_new_rock_physics_models.py)
  - [tests/test_rock_physics.py](tests/test_rock_physics.py)

## High-level flow

```mermaid
flowchart TD
  A[Load config] --> B[Create rock physics model]
  B --> C[Choose strategy]
  C -->|pseudolabels| D[Augment features with physics VS]
  C -->|loss_function| E[Trainer computes physics_pred and uses PhysicsGuidedLoss]
  C -->|transfer_learning| F[Stage 1 pretrain on physics targets]
  F --> G[Stage 2 fine-tune on true targets]
  D --> H[Train BiGRU]
  E --> H[Train BiGRU]
  G --> H[Train BiGRU]
  H --> I[Evaluate with blind test protocol]
```

## Conclusion

The repository already implements all three physics-guided strategies described in the guide:
- Pseudolabel augmentation is demonstrated in the example pipeline and available as a utility.
- Physics-guided loss matches the paper’s adaptive formulation and is correctly unit-aware in the trainer.
- Transfer learning follows the two-stage procedure from Zhao et al., leveraging unit-consistent physics targets.

Closing the small gaps noted above will standardize pseudolabel augmentation across sequence data and unit conversions, and add optional domain adaptation on top of the robust blind-test evaluation already present.