# Physics-Guided Strategies Integration in PhysBIGRU

This document provides a detailed plan to incorporate physics-guidance
strategies into the current PhysBIGRU codebase, inspired by <PERSON> et
al. (2024).

## Core Strategies

### 1. Physics-Guided Pseudolabels

-   Construct pseudolabels using empirical models such as:
    -   Mudrock line (<PERSON><PERSON><PERSON> et al., 1985)
    -   Empirical VP--VS relationships from training well logs
    -   Multiparameter regression combining GR, density, VP, resistivity
-   Integrate pseudolabels as additional input features to BiGRU.

### 2. Physics-Guided Loss Function

-   Combine conventional regression loss (MSE) with physics consistency
    loss:
    -   `lossa = MSE(predicted, actual)`
    -   `lossb = MSE(predicted, empirical physics model)`
-   Final loss function: `Loss = lossa + min(lossa, lossb)`

### 3. Transfer Learning

-   Pre-train BiGRU on pseudolabels derived from empirical models.
-   Fine-tune the model on real VS labels with a reduced learning rate.
-   Use domain adaptation when moving across fields or formations.

## Implementation Steps

1.  **Data Preparation**
    -   Normalize input logs (GR, VP, density, resistivity) to (-1,1).
    -   Compute pseudolabels using equations (5--7) from <PERSON> et
        al. (2024).
2.  **Model Adjustments**
    -   Extend current BiGRU class to accept augmented inputs (original
        logs + pseudolabels).
    -   Add a configurable loss function wrapper that supports combined
        physics-guided loss.
3.  **Training Pipeline**
    -   Train with pseudolabels + real logs as features.
    -   Use physics-guided loss during backpropagation.
    -   Apply transfer learning by initializing weights from
        pseudolabel-trained model.
4.  **Evaluation**
    -   Compare performance with and without physics guidance.
    -   Blind test on wells excluded from training to ensure
        generalization.
    -   Report RMSE and correlation coefficient improvements.

## References

-   Zhao et al. (2024). Rock-physics-guided machine learning for shear
    sonic log prediction. *Geophysics, 89*(1), D75--D87.
