"""
Physics coupling utilities for unit conversion and scaler-aware physics predictions.

This module centralizes the logic for converting between normalized/physical units
and computing physics-based predictions consistently across different training strategies.
"""

import numpy as np
import torch
from typing import Optional, Dict, Any, Union, Tuple


class PhysicsCouplingManager:
    """
    Manages unit conversions and scaler-aware physics predictions.
    
    This class centralizes the logic that was previously duplicated across
    PhysicsGuidedTrainer.compute_physics_predictions() and train_epoch().
    """
    
    def __init__(self, config: Dict[str, Any], vp_index: int, rock_physics_model):
        """
        Initialize the physics coupling manager.
        
        Args:
            config: Configuration dictionary containing physics_coupling settings
            vp_index: Index of VP feature in the feature array
            rock_physics_model: Rock physics model for predictions
        """
        self.config = config
        self.vp_index = vp_index
        self.rock_physics_model = rock_physics_model
        
        # Extract physics coupling configuration
        physics_config = config.get("physics_coupling", {})
        self.vp_units = physics_config.get("vp_units", "m/s")
        self.physics_units = physics_config.get("physics_units", "km/s")
        self.conversion_factor = physics_config.get("conversion_factor", 0.001)
        
        # Fallback VP range for cases without preprocessor
        self.fallback_vp_range = (2000.0, 6000.0)  # Typical VP range in m/s
    
    def denormalize_vp_to_physical(
        self, 
        vp_normalized: Union[torch.Tensor, np.ndarray], 
        preprocessor: Optional[Any] = None
    ) -> np.ndarray:
        """
        Convert normalized VP values to physical units (m/s).
        
        Args:
            vp_normalized: Normalized VP values [B, T] or [N]
            preprocessor: Data preprocessor with scalers (optional)
            
        Returns:
            VP values in physical units (m/s)
        """
        # Convert to numpy if needed
        if isinstance(vp_normalized, torch.Tensor):
            vp_normalized = vp_normalized.cpu().numpy()
        
        original_shape = vp_normalized.shape
        
        if preprocessor is not None and hasattr(preprocessor, 'scalers') and 'features' in preprocessor.scalers:
            # Denormalize using feature scaler
            vp_reshaped = vp_normalized.reshape(-1, 1)
            
            # Create dummy feature array with VP in correct position
            n_features = len(self.config["data"]["features"])
            dummy_features = np.zeros((vp_reshaped.shape[0], n_features))
            dummy_features[:, self.vp_index] = vp_reshaped.flatten()
            
            # Denormalize
            vp_denorm = preprocessor.scalers['features'].inverse_transform(dummy_features)
            vp_physical_ms = vp_denorm[:, self.vp_index].reshape(original_shape)
        else:
            # Fallback: assume normalized values represent typical VP range
            vp_min, vp_max = self.fallback_vp_range
            vp_physical_ms = vp_normalized * (vp_max - vp_min) + vp_min
            
        return vp_physical_ms
    
    def convert_vp_to_physics_units(self, vp_physical_ms: np.ndarray) -> np.ndarray:
        """
        Convert VP from physical units (m/s) to physics model units (km/s).
        
        Args:
            vp_physical_ms: VP values in m/s
            
        Returns:
            VP values in km/s
        """
        return vp_physical_ms * self.conversion_factor
    
    def convert_vs_to_target_units(self, vs_physics_kms: np.ndarray) -> np.ndarray:
        """
        Convert VS from physics model units (km/s) to target units (m/s).
        
        Args:
            vs_physics_kms: VS values in km/s from physics model
            
        Returns:
            VS values in m/s
        """
        return vs_physics_kms * 1000.0
    
    def normalize_physics_predictions(
        self, 
        physics_pred_ms: np.ndarray, 
        preprocessor: Optional[Any] = None
    ) -> np.ndarray:
        """
        Normalize physics predictions to match target scale.
        
        Args:
            physics_pred_ms: Physics predictions in m/s
            preprocessor: Data preprocessor with scalers (optional)
            
        Returns:
            Normalized physics predictions or raw m/s values
        """
        if preprocessor is not None and hasattr(preprocessor, 'scalers') and 'targets' in preprocessor.scalers:
            original_shape = physics_pred_ms.shape
            physics_pred_ms_flat = physics_pred_ms.reshape(-1, 1)
            physics_pred_norm_flat = preprocessor.scalers['targets'].transform(physics_pred_ms_flat)
            return physics_pred_norm_flat.reshape(original_shape)
        else:
            # Return raw m/s predictions
            return physics_pred_ms
    
    def compute_physics_predictions(
        self,
        features: Union[torch.Tensor, np.ndarray],
        preprocessor: Optional[Any] = None,
        feature_dict: Optional[Dict[str, np.ndarray]] = None
    ) -> np.ndarray:
        """
        Compute physics-based predictions with proper unit handling.

        This is the main method that combines all the unit conversion steps
        to produce physics predictions in the same scale as targets.

        Args:
            features: Input features tensor [B, T, F] or [N, F]
            preprocessor: Data preprocessor for unit conversion (optional)
            feature_dict: For multiparameter models, dict with feature arrays (optional)

        Returns:
            Physics-based VS predictions in same units/scale as targets
        """
        # Check if this is a multiparameter model
        is_multiparameter = hasattr(self.rock_physics_model, 'feature_names') and \
                           len(getattr(self.rock_physics_model, 'feature_names', [])) > 1

        if feature_dict is not None:
            # Use provided feature dictionary for multiparameter models
            physics_pred_kms = self.rock_physics_model.predict(feature_dict)
        elif is_multiparameter:
            # Prepare feature dictionary for multiparameter models
            feature_dict = self.prepare_multiparameter_features(features, preprocessor)
            physics_pred_kms = self.rock_physics_model.predict(feature_dict)
        else:
            # Extract VP from features for VP-only models
            if isinstance(features, torch.Tensor):
                if features.dim() == 3:  # [B, T, F]
                    vp_normalized = features[:, :, self.vp_index]
                else:  # [N, F]
                    vp_normalized = features[:, self.vp_index]
            else:  # numpy array
                if features.ndim == 3:  # [B, T, F]
                    vp_normalized = features[:, :, self.vp_index]
                else:  # [N, F]
                    vp_normalized = features[:, self.vp_index]

            # Step 1: Denormalize VP to physical units (m/s)
            vp_physical_ms = self.denormalize_vp_to_physical(vp_normalized, preprocessor)

            # Step 2: Convert to physics model units (km/s)
            vp_physics_kms = self.convert_vp_to_physics_units(vp_physical_ms)

            # Step 3: Get physics predictions (returns VS in km/s)
            physics_pred_kms = self.rock_physics_model.predict(vp_physics_kms)

        # Step 4: Convert physics predictions to target units (m/s)
        physics_pred_ms = self.convert_vs_to_target_units(physics_pred_kms)

        # Step 5: Normalize if targets are normalized
        physics_predictions = self.normalize_physics_predictions(physics_pred_ms, preprocessor)

        return physics_predictions
    
    def prepare_multiparameter_features(
        self,
        features: Union[torch.Tensor, np.ndarray],
        preprocessor: Optional[Any] = None
    ) -> Dict[str, np.ndarray]:
        """
        Prepare feature dictionary for multiparameter physics models.

        Args:
            features: Input features tensor [B, T, F] or [N, F]
            preprocessor: Data preprocessor for unit conversion (optional)

        Returns:
            Dictionary with feature arrays in physical units for multiparameter model
        """
        # Convert to numpy if needed
        if isinstance(features, torch.Tensor):
            features_np = features.cpu().numpy()
        else:
            features_np = features

        original_shape = features_np.shape
        feature_names = self.config["data"]["features"]

        # Multiparameter model expects: ['GR', 'DEN', 'VP', 'RES']
        required_features = ['GR', 'DEN', 'VP', 'RES']

        # Create mapping from config feature names to multiparameter names
        feature_mapping = self._create_feature_mapping(feature_names, required_features)

        # Prepare feature dictionary
        feature_dict = {}

        if preprocessor is not None and hasattr(preprocessor, 'scalers') and 'features' in preprocessor.scalers:
            # Denormalize all features to physical units
            if features_np.ndim == 3:  # [B, T, F]
                batch_size, seq_len, n_features = features_np.shape
                features_reshaped = features_np.reshape(-1, n_features)
                features_denorm = preprocessor.scalers['features'].inverse_transform(features_reshaped)
                features_denorm = features_denorm.reshape(original_shape)
            else:  # [N, F]
                features_denorm = preprocessor.scalers['features'].inverse_transform(features_np)

            # Extract and map features to multiparameter names
            for mp_name, config_name in feature_mapping.items():
                if config_name is not None:
                    config_idx = feature_names.index(config_name)
                    if features_denorm.ndim == 3:
                        feature_values = features_denorm[:, :, config_idx]
                    else:
                        feature_values = features_denorm[:, config_idx]

                    # Apply unit conversions for specific features
                    if mp_name == 'VP':
                        # Convert VP from m/s to km/s for multiparameter model
                        feature_dict[mp_name] = feature_values * self.conversion_factor
                    elif mp_name == 'RES':
                        # Ensure RES is in linear ohm.m (model will log-transform internally)
                        feature_dict[mp_name] = np.maximum(feature_values, 1e-6)  # Avoid zeros
                    else:
                        feature_dict[mp_name] = feature_values
                else:
                    # Use default values for missing features
                    shape = features_denorm.shape[:-1]  # Remove last dimension
                    feature_dict[mp_name] = self._get_default_feature_values(mp_name, shape)
        else:
            # Use fallback ranges for each feature type
            fallback_ranges = {
                'GR': (0.0, 200.0),          # API
                'DEN': (1.5, 3.0),           # g/cm3
                'VP': (2.0, 6.0),            # km/s (already converted)
                'RES': (0.1, 1000.0),        # ohm.m
            }

            for mp_name in required_features:
                config_name = feature_mapping.get(mp_name)
                if config_name is not None:
                    config_idx = feature_names.index(config_name)
                    if features_np.ndim == 3:
                        feature_values = features_np[:, :, config_idx]
                    else:
                        feature_values = features_np[:, config_idx]

                    # Apply fallback scaling
                    if mp_name in fallback_ranges:
                        vmin, vmax = fallback_ranges[mp_name]
                        if mp_name == 'VP':
                            # VP fallback range is already in km/s
                            feature_dict[mp_name] = feature_values * (vmax - vmin) + vmin
                        else:
                            feature_dict[mp_name] = feature_values * (vmax - vmin) + vmin
                    else:
                        feature_dict[mp_name] = feature_values
                else:
                    # Use default values for missing features
                    shape = features_np.shape[:-1]  # Remove last dimension
                    feature_dict[mp_name] = self._get_default_feature_values(mp_name, shape)

        return feature_dict

    def _create_feature_mapping(self, config_features: list, required_features: list) -> Dict[str, Optional[str]]:
        """
        Create mapping from multiparameter feature names to config feature names.

        Args:
            config_features: Feature names from config
            required_features: Required feature names for multiparameter model

        Returns:
            Dictionary mapping multiparameter names to config names
        """
        mapping = {}

        # Common name variations for each required feature
        name_variations = {
            'GR': ['GR', 'GAMMA', 'GAMMA_RAY'],
            'DEN': ['DEN', 'RHOB', 'DENSITY', 'BULK_DENSITY'],
            'VP': ['VP', 'P-WAVE', 'DTCO', 'DTC', 'COMPRESSIONAL'],
            'RES': ['RES', 'RT', 'RESISTIVITY', 'ILD', 'LLD']
        }

        for req_feature in required_features:
            mapping[req_feature] = None
            variations = name_variations.get(req_feature, [req_feature])

            # Find first matching variation in config features
            for variation in variations:
                if variation in config_features:
                    mapping[req_feature] = variation
                    break

        return mapping

    def _get_default_feature_values(self, feature_name: str, shape: tuple) -> np.ndarray:
        """
        Get default values for missing features.

        Args:
            feature_name: Name of the feature
            shape: Shape of the output array

        Returns:
            Default feature values
        """
        defaults = {
            'GR': 100.0,    # API - typical shale value
            'DEN': 2.3,     # g/cm3 - typical sediment density
            'VP': 3.5,      # km/s - typical VP
            'RES': 10.0     # ohm.m - typical resistivity
        }

        default_value = defaults.get(feature_name, 1.0)
        return np.full(shape, default_value)
