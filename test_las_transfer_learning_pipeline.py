"""
Enhanced LAS ML Pipeline with Transfer Learning
This script demonstrates the complete pipeline from LAS file loading to model training 
with transfer learning and comprehensive visualization.
"""
import os
import sys
import yaml
import json
import numpy as np
import matplotlib.pyplot as plt
import torch
from torch.utils.data import DataLoader, TensorDataset
import pandas as pd
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import seaborn as sns
from tqdm import tqdm
from datetime import datetime
from pathlib import Path

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data.las_loader import LASLoader, WellLogDataProcessor
from data.preprocessing import WellLogPreprocessor, split_data, apply_log_transforms
from models.neural_networks import BiGRU
from models.rock_physics import RockPhysicsFactory
from training.strategies import PhysicsGuidanceStrategy, StrategyHandler
from training.trainer import PhysicsGuidedTrainer
from training.transfer_learning import TransferLearningHelper


class LASTransferLearningPipelineTest:
    """Enhanced test class for LAS to ML pipeline with transfer learning."""
    
    def __init__(self, config_path: str = "configs/default_config.yaml",
                 output_dir: str = "output", enable_transfer_learning: bool = True,
                 enable_blind_test: bool = False):
        self.config_path = config_path
        self.output_dir = Path(output_dir)
        self.enable_transfer_learning = enable_transfer_learning
        self.enable_blind_test = enable_blind_test
        self.config = self._load_config()
        self.las_files = self._find_las_files()
        self.results = {}
        self.well_info = {}

        # Initialize blind test protocol if enabled
        self.blind_test = None
        if self.enable_blind_test:
            from src.utils.blind_test_protocol import BlindTestProtocol
            self.blind_test = BlindTestProtocol(
                las_directory=os.path.join(os.path.dirname(__file__), 'Las'),
                config=self.config,
                output_dir=self.output_dir
            )
        
        # Create output directory
        self._setup_output_directory()
        
    def _setup_output_directory(self):
        """Create output directory structure."""
        try:
            self.output_dir.mkdir(parents=True, exist_ok=True)
            (self.output_dir / "visualizations").mkdir(exist_ok=True)
            (self.output_dir / "models").mkdir(exist_ok=True)
            (self.output_dir / "results").mkdir(exist_ok=True)
            print(f"Output directory setup: {self.output_dir.absolute()}")
        except Exception as e:
            print(f"Warning: Could not create output directory: {e}")
            self.output_dir = Path(".")
    
    def _load_config(self) -> dict:
        """Load configuration with transfer learning settings."""
        if os.path.exists(self.config_path):
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)
        else:
            config = self._create_default_config()
        
        # Ensure transfer learning section exists
        if 'transfer_learning' not in config:
            config['transfer_learning'] = {
                'enabled': self.enable_transfer_learning,
                'pretrain_epochs': 30,
                'pretrain_lr': 0.001,
                'finetune_epochs': 50,
                'finetune_lr': 0.0001,
                'early_stopping_patience': 10,
                'save_pretrained': True,
                'pretrained_path': str(self.output_dir / "models" / "pretrained_las_model.pth")
            }
        else:
            config['transfer_learning']['enabled'] = self.enable_transfer_learning
            
        return config
    
    def _create_default_config(self) -> dict:
        """Create default configuration."""
        return {
            'model': {
                'type': 'BiGRU',
                'params': {
                    'input_dim': 4,
                    'hidden_dim': 32,
                    'output_dim': 1,
                    'num_layers': 2,
                    'dropout': 0.1
                }
            },
            'rock_physics': {
                'model_type': 'empirical_vpvs',  # Use new empirical model
                'params': {'a': 0.8619, 'b': 0.8621}
            },
            'training': {
                'strategy': 'loss_function',
                'batch_size': 32,
                'learning_rate': 0.001,
                'epochs': 100,
                'early_stopping_patience': 15
            },
            'data': {
                'features': ['P-WAVE', 'RHOB', 'PHIE', 'RT'],
                'target': ['S-WAVE'],
                'vp_feature_name': 'P-WAVE',
                'train_test_split': 0.8,
                'sequence_length': 50,
                'sequence_stride': 10
            },
            'physics_coupling': {
                'vp_units': 'm/s',
                'physics_units': 'km/s', 
                'conversion_factor': 0.001
            }
        }
    
    def _find_las_files(self) -> list:
        """Find LAS files."""
        las_dir = os.path.join(os.path.dirname(__file__), 'Las')
        if not os.path.exists(las_dir):
            raise FileNotFoundError(f"LAS directory not found: {las_dir}")
        
        las_files = [
            os.path.join(las_dir, f) for f in os.listdir(las_dir)
            if f.lower().endswith('.las')
        ]
        
        if not las_files:
            raise FileNotFoundError(f"No LAS files found in {las_dir}")
        
        print(f"Found {len(las_files)} LAS files")
        return las_files
    
    def run_complete_test(self) -> dict:
        """Run complete pipeline test."""
        if self.enable_blind_test:
            return self._run_blind_test()
        else:
            return self._run_standard_test()

    def _run_blind_test(self) -> dict:
        """Run blind test protocol with transfer learning."""
        print("="*80)
        print("BLIND TEST PROTOCOL - TRANSFER LEARNING PIPELINE")
        print("="*80)

        try:
            # Set deterministic seed for reproducibility
            self._set_deterministic_seed(42)

            # Step 1: Process wells for blind test
            print("\n1. Processing wells for blind test...")
            processor = WellLogDataProcessor(self.config)

            training_data, test_data = self.blind_test.process_wells_for_blind_test(processor)

            # Step 2: Prepare training sequences
            print("\n2. Preparing training sequences...")
            train_features, train_targets = self.blind_test.prepare_training_sequences(
                self.config['data']['sequence_length'],
                self.config['data']['sequence_stride']
            )

            # Step 3: Prepare test sequences
            print("\n3. Preparing test sequences...")
            test_sequences = self.blind_test.prepare_test_sequences(
                self.config['data']['sequence_length'],
                self.config['data']['sequence_stride']
            )

            # Step 4: Preprocess data
            print("\n4. Preprocessing data...")
            preprocessor = WellLogPreprocessor(normalization='minmax', feature_engineering=False)

            # Split training data for validation
            val_split = 0.15
            n_train_samples = len(train_features)
            n_val_samples = int(val_split * n_train_samples)

            # Shuffle indices
            indices = np.arange(n_train_samples)
            np.random.shuffle(indices)

            train_idx = indices[n_val_samples:]
            val_idx = indices[:n_val_samples]

            train_feat_split = train_features[train_idx]
            train_targ_split = train_targets[train_idx]
            val_feat_split = train_features[val_idx]
            val_targ_split = train_targets[val_idx]

            # Fit preprocessor on training data only
            train_feat_2d = train_feat_split.reshape(-1, train_feat_split.shape[-1])
            train_targ_2d = train_targ_split.reshape(-1, train_targ_split.shape[-1])

            preprocessor.fit(train_feat_2d, train_targ_2d,
                            feature_names=self.config['data']['features'],
                            target_names=self.config['data']['target'])

            # Transform training and validation data
            def transform_split(features, targets):
                feat_2d = features.reshape(-1, features.shape[-1])
                targ_2d = targets.reshape(-1, targets.shape[-1])

                feat_norm = preprocessor.transform_features(feat_2d)
                targ_norm = preprocessor.transform_targets(targ_2d)

                feat_3d = feat_norm.reshape(features.shape)
                targ_3d = targ_norm.reshape(targets.shape)

                return feat_3d, targ_3d

            train_feat_norm, train_targ_norm = transform_split(train_feat_split, train_targ_split)
            val_feat_norm, val_targ_norm = transform_split(val_feat_split, val_targ_split)

            print(f"   Training sequences: {train_feat_norm.shape}")
            print(f"   Validation sequences: {val_feat_norm.shape}")

            # Step 5: Setup model
            print("\n5. Setting up model...")
            model, trainer, loss_fn = self._setup_model(train_feat_norm.shape[-1], preprocessor)

            # Step 6: Train with transfer learning
            if self.enable_transfer_learning:
                print("\n6. Training with transfer learning...")
                training_results = self._train_with_transfer_learning_blind_test(
                    model, trainer, loss_fn, train_feat_norm, train_targ_norm, val_feat_norm, val_targ_norm
                )
            else:
                print("\n6. Training with standard method...")
                training_results = self._train_standard_blind_test(
                    model, trainer, loss_fn, train_feat_norm, train_targ_norm, val_feat_norm, val_targ_norm
                )

            # Step 7: Evaluate on test wells
            print("\n7. Evaluating model on test wells...")
            per_well_results = self.blind_test.evaluate_model_per_well(
                model, preprocessor, test_sequences
            )

            # Step 8: Calculate aggregated metrics
            print("\n8. Calculating aggregated metrics...")
            aggregated_metrics = self.blind_test.calculate_aggregated_metrics(per_well_results)

            # Step 9: Save results
            print("\n9. Saving blind test results...")
            json_path, csv_path = self.blind_test.save_results(
                per_well_results, aggregated_metrics, training_results
            )

            # Step 10: Create visualizations
            print("\n10. Creating blind test visualizations...")
            self.blind_test.create_blind_test_visualizations(
                per_well_results, aggregated_metrics
            )

            # Step 11: Print summary
            self.blind_test.print_blind_test_summary(per_well_results, aggregated_metrics)

            # Compile results
            blind_test_results = {
                'test_type': 'blind_test_transfer_learning',
                'training_well': self.blind_test.training_well,
                'test_wells': list(per_well_results.keys()),
                'training_results': training_results,
                'per_well_results': {k: v['metrics'] for k, v in per_well_results.items()},
                'aggregated_metrics': aggregated_metrics,
                'transfer_learning_enabled': self.enable_transfer_learning,
                'output_paths': {
                    'json_results': str(json_path),
                    'csv_results': str(csv_path),
                    'visualizations': str(self.blind_test.blind_test_dir / "visualizations")
                }
            }

            print("\n" + "="*80)
            print("BLIND TEST PROTOCOL WITH TRANSFER LEARNING COMPLETED!")
            print("="*80)

            return blind_test_results

        except Exception as e:
            print(f"\n❌ BLIND TEST FAILED: {e}")
            import traceback
            traceback.print_exc()
            return {'error': str(e), 'test_type': 'blind_test_transfer_learning'}

    def _run_standard_test(self) -> dict:
        """Run the standard transfer learning pipeline test."""
        print("="*80)
        print("🚀 ENHANCED LAS ML PIPELINE WITH TRANSFER LEARNING")
        print("="*80)

        try:
            # Load and process data
            print("\n1. 📊 Loading and processing LAS data...")
            features, targets, successful_files = self._process_data()
            
            # Create sequences and split data
            print("\n2. 🔄 Creating sequences and splitting data...")
            seq_features, seq_targets = self._prepare_sequences(features, targets)
            data_splits = self._split_data(seq_features, seq_targets)
            
            # Preprocess data
            print("\n3. ⚙️ Preprocessing data...")
            preprocessor, processed_splits = self._preprocess_splits(data_splits)
            
            # Setup model
            print("\n4. 🤖 Setting up model...")
            model, trainer, loss_fn = self._setup_model(processed_splits[0].shape[-1], preprocessor)
            
            # Training with or without transfer learning
            if self.enable_transfer_learning:
                print("\n5. 🎯 Training with Transfer Learning...")
                training_results = self._train_with_transfer_learning(
                    model, trainer, loss_fn, processed_splits
                )
            else:
                print("\n5. 🎯 Training with Standard Method...")
                training_results = self._train_standard(
                    model, trainer, loss_fn, processed_splits
                )
            
            self.results['training'] = training_results
            
            # Evaluate model
            print("\n6. 📈 Evaluating model...")
            evaluation_results = self._evaluate_model(
                model, processed_splits[2], processed_splits[5], preprocessor
            )
            self.results['evaluation'] = evaluation_results
            
            # Create comprehensive visualizations
            print("\n7. 📊 Creating visualizations...")
            self._create_comprehensive_visualizations(
                processed_splits, model, preprocessor, evaluation_results, training_results
            )

            # Create well-specific visualizations (individual wells + comparison grid)
            print("\n8. 🛢️ Creating well-specific visualizations...")
            self._create_well_specific_visualizations(
                processed_splits, model, preprocessor, evaluation_results
            )
            
            print("\n" + "="*80)
            print("✅ PIPELINE COMPLETED SUCCESSFULLY!")
            print("="*80)
            self._print_summary()
            
        except Exception as e:
            print(f"\n❌ ERROR: {e}")
            import traceback
            traceback.print_exc()
            
        return self.results
    
    def _process_data(self):
        """Process LAS files and extract features/targets, while tracking individual wells for plotting."""
        processor = WellLogDataProcessor(self.config)
        all_features = []
        all_targets = []
        successful_files = []

        # Track individual well data for later visualization
        self.well_data_individual = {}
        
        for las_file in self.las_files:
            try:
                features, targets = processor.process_single_well(las_file)
                if features is not None and targets is not None and len(features) > 0:
                    all_features.append(features)
                    all_targets.append(targets)
                    successful_files.append(las_file)

                    well_name = os.path.basename(las_file)
                    # Store per-well raw data for plotting (depth_range may be estimated later)
                    self.well_data_individual[well_name] = {
                        'features': features,
                        'targets': targets,
                        'depth_range': (0.0, 0.0),  # Optional: update if depth is available from processor
                        'file_path': las_file
                    }

                    print(f"  ✓ {well_name}: {len(features)} points")
            except Exception as e:
                print(f"  ❌ {os.path.basename(las_file)}: {e}")
        
        if not all_features:
            raise ValueError("No valid data extracted from LAS files")
        
        return np.concatenate(all_features), np.concatenate(all_targets), successful_files
    
    def _prepare_sequences(self, features, targets):
        """Create sequences for time series training."""
        seq_length = self.config['data']['sequence_length']
        stride = self.config['data']['sequence_stride']
        
        seq_features = []
        seq_targets = []
        
        for i in range(0, len(features) - seq_length + 1, stride):
            seq_features.append(features[i:i + seq_length])
            seq_targets.append(targets[i:i + seq_length])
        
        return np.array(seq_features), np.array(seq_targets)
    
    def _split_data(self, features, targets):
        """Split data into train/val/test sets."""
        n_samples = len(features)
        train_size = int(0.7 * n_samples)
        val_size = int(0.15 * n_samples)
        
        indices = np.arange(n_samples)
        np.random.shuffle(indices)
        
        train_idx = indices[:train_size]
        val_idx = indices[train_size:train_size + val_size]
        test_idx = indices[train_size + val_size:]
        
        return (
            features[train_idx], features[val_idx], features[test_idx],
            targets[train_idx], targets[val_idx], targets[test_idx]
        )
    
    def _preprocess_splits(self, data_splits):
        """Preprocess all data splits."""
        train_features, val_features, test_features, train_targets, val_targets, test_targets = data_splits
        
        # Create preprocessor and fit on training data only
        preprocessor = WellLogPreprocessor(normalization='minmax', feature_engineering=False)
        
        # Reshape for preprocessing
        train_feat_2d = train_features.reshape(-1, train_features.shape[-1])
        train_targ_2d = train_targets.reshape(-1, train_targets.shape[-1])
        
        # Fit the preprocessor on training data
        preprocessor.fit(train_feat_2d, train_targ_2d, 
                        feature_names=self.config['data']['features'], 
                        target_names=self.config['data']['target'])
        
        # Transform all splits
        def transform_split(features, targets):
            feat_2d = features.reshape(-1, features.shape[-1])
            targ_2d = targets.reshape(-1, targets.shape[-1])
            
            feat_norm = preprocessor.transform_features(feat_2d)
            targ_norm = preprocessor.transform_targets(targ_2d)
            
            feat_3d = feat_norm.reshape(features.shape)
            targ_3d = targ_norm.reshape(targets.shape)
            
            return feat_3d, targ_3d
        
        train_feat_norm, train_targ_norm = transform_split(train_features, train_targets)
        val_feat_norm, val_targ_norm = transform_split(val_features, val_targets)
        test_feat_norm, test_targ_norm = transform_split(test_features, test_targets)
        
        return preprocessor, (
            train_feat_norm, val_feat_norm, test_feat_norm,
            train_targ_norm, val_targ_norm, test_targ_norm
        )
    
    def _setup_model(self, input_dim, preprocessor):
        """Setup model components."""
        self.config['model']['params']['input_dim'] = input_dim
        
        rock_physics_model = RockPhysicsFactory.create(
            self.config['rock_physics']['model_type'],
            **self.config['rock_physics']['params']
        )
        
        strategy = PhysicsGuidanceStrategy(self.config['training']['strategy'])
        strategy_handler = StrategyHandler(strategy, rock_physics_model)
        
        model = BiGRU(**self.config['model']['params'])
        
        trainer = PhysicsGuidedTrainer(
            model=model,
            rock_physics_model=rock_physics_model,
            strategy_handler=strategy_handler,
            config=self.config,
            preprocessor=preprocessor
        )
        
        loss_fn = strategy_handler.get_loss_function()
        
        print(f"  Model parameters: {sum(p.numel() for p in model.parameters()):,}")
        print(f"  Rock physics model: {rock_physics_model.name}")
        
        return model, trainer, loss_fn
    
    def _train_with_transfer_learning(self, model, trainer, loss_fn, processed_splits):
        """Train with transfer learning."""
        train_feat, val_feat, test_feat, train_targ, val_targ, test_targ = processed_splits
        
        # Create data loaders
        train_loader = DataLoader(
            TensorDataset(torch.FloatTensor(train_feat), torch.FloatTensor(train_targ)),
            batch_size=self.config['training']['batch_size'], shuffle=True
        )
        val_loader = DataLoader(
            TensorDataset(torch.FloatTensor(val_feat), torch.FloatTensor(val_targ)),
            batch_size=self.config['training']['batch_size'], shuffle=False
        )
        
        # Create transfer learning helper
        tl_helper = TransferLearningHelper(self.config, trainer, model, loss_fn)
        
        # Run transfer learning
        results = tl_helper.run_transfer_learning(train_loader, val_loader, preprocessor=trainer.preprocessor)
        
        # Plot training history
        try:
            tl_helper.plot_training_history(str(self.output_dir / "visualizations" / "transfer_learning_history.png"))
        except:
            print("  Could not create training history plot")
        
        results['method'] = 'transfer_learning'
        results['tl_helper'] = tl_helper
        
        return results
    
    def _train_standard(self, model, trainer, loss_fn, processed_splits):
        """Train with standard method."""
        train_feat, val_feat, test_feat, train_targ, val_targ, test_targ = processed_splits
        
        train_loader = DataLoader(
            TensorDataset(torch.FloatTensor(train_feat), torch.FloatTensor(train_targ)),
            batch_size=self.config['training']['batch_size'], shuffle=True
        )
        val_loader = DataLoader(
            TensorDataset(torch.FloatTensor(val_feat), torch.FloatTensor(val_targ)),
            batch_size=self.config['training']['batch_size'], shuffle=False
        )
        
        optimizer = torch.optim.Adam(model.parameters(), lr=self.config['training']['learning_rate'])
        
        history = {'train_loss': [], 'val_loss': [], 'val_rmse': [], 'val_correlation': []}
        best_val_loss = float('inf')
        
        for epoch in tqdm(range(self.config['training']['epochs']), desc="Training"):
            train_loss = trainer.train_epoch(train_loader, optimizer, loss_fn)
            val_metrics = trainer.evaluate(val_loader, torch.nn.MSELoss())
            
            history['train_loss'].append(train_loss)
            history['val_loss'].append(val_metrics['loss'])
            history['val_rmse'].append(val_metrics['rmse'])
            history['val_correlation'].append(val_metrics['correlation'])
            
            if val_metrics['loss'] < best_val_loss:
                best_val_loss = val_metrics['loss']
                torch.save(model.state_dict(), self.output_dir / "models" / "best_standard_model.pth")
        
        # Load best model
        model.load_state_dict(torch.load(self.output_dir / "models" / "best_standard_model.pth"))
        
        return {
            'method': 'standard',
            'best_val_loss': best_val_loss,
            'history': history
        }
    
    def _evaluate_model(self, model, test_features, test_targets, preprocessor):
        """Evaluate model performance."""
        model.eval()
        
        # Get device from model parameters
        device = next(model.parameters()).device
        
        with torch.no_grad():
            test_tensor = torch.FloatTensor(test_features).to(device)
            predictions = model(test_tensor).cpu().numpy()
        
        # Transform back to original scale
        predictions_orig = preprocessor.inverse_transform_targets(predictions)
        targets_orig = preprocessor.inverse_transform_targets(test_targets)
        
        # Calculate metrics
        pred_flat = predictions_orig.flatten()
        targ_flat = targets_orig.flatten()
        
        # Remove invalid values
        valid_mask = np.isfinite(pred_flat) & np.isfinite(targ_flat)
        pred_clean = pred_flat[valid_mask]
        targ_clean = targ_flat[valid_mask]
        
        metrics = {
            'rmse': np.sqrt(mean_squared_error(targ_clean, pred_clean)),
            'mae': mean_absolute_error(targ_clean, pred_clean),
            'r2': r2_score(targ_clean, pred_clean),
            'correlation': np.corrcoef(pred_clean, targ_clean)[0, 1]
        }
        
        print(f"  📊 Test Results:")
        print(f"    RMSE: {metrics['rmse']:.2f}")
        print(f"    R²: {metrics['r2']:.4f}")
        print(f"    Correlation: {metrics['correlation']:.4f}")
        
        return metrics
    
    def _create_comprehensive_visualizations(self, processed_splits, model, preprocessor, evaluation_results, training_results):
        """Create comprehensive visualizations."""
        train_feat, val_feat, test_feat, train_targ, val_targ, test_targ = processed_splits
        
        # Create figure with subplots
        fig = plt.figure(figsize=(20, 16))
        
        # 1. Training history
        if training_results['method'] == 'transfer_learning':
            self._plot_transfer_learning_history(fig, training_results, subplot_pos=221)
        else:
            self._plot_standard_history(fig, training_results, subplot_pos=221)
        
        # 2. Predictions vs Targets
        self._plot_predictions_vs_targets(fig, model, test_feat, test_targ, preprocessor, subplot_pos=222)
        
        # 3. Physics comparison
        self._plot_physics_comparison(fig, test_feat, test_targ, preprocessor, subplot_pos=223)
        
        # 4. Error analysis
        self._plot_error_analysis(fig, model, test_feat, test_targ, preprocessor, subplot_pos=224)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / "visualizations" / "comprehensive_analysis.png", 
                   dpi=300, bbox_inches='tight')
        plt.close()

        # Create extended visualizations similar to the non-transfer pipeline
        try:
            self._create_extended_visualizations(
                processed_splits, model, preprocessor, evaluation_results, training_results
            )
        except Exception as e:
            print(f"  Warning: Extended visualization generation failed: {e}")
        
        print(f"  📊 Visualizations saved to {self.output_dir / 'visualizations'}")
    
    def _plot_transfer_learning_history(self, fig, training_results, subplot_pos):
        """Plot transfer learning training history."""
        ax = fig.add_subplot(subplot_pos)
        tl_helper = training_results['tl_helper']
        history = tl_helper.get_training_history()
        
        if history['pretrain_history']:
            pretrain_epochs = [h['epoch'] for h in history['pretrain_history']]
            pretrain_losses = [h['val_loss'] for h in history['pretrain_history']]
            ax.plot(pretrain_epochs, pretrain_losses, 'b-', label='Pretrain (Physics)', alpha=0.7)
        
        if history['finetune_history']:
            finetune_epochs = [h['epoch'] + len(history['pretrain_history']) for h in history['finetune_history']]
            finetune_losses = [h['val_loss'] for h in history['finetune_history']]
            ax.plot(finetune_epochs, finetune_losses, 'r-', label='Finetune (True)', alpha=0.7)
        
        ax.set_xlabel('Epoch')
        ax.set_ylabel('Validation Loss')
        ax.set_title('Transfer Learning Training History')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_standard_history(self, fig, training_results, subplot_pos):
        """Plot standard training history."""
        ax = fig.add_subplot(subplot_pos)
        history = training_results['history']
        
        epochs = range(1, len(history['train_loss']) + 1)
        ax.plot(epochs, history['train_loss'], 'b-', label='Train Loss', alpha=0.7)
        ax.plot(epochs, history['val_loss'], 'r-', label='Val Loss', alpha=0.7)
        
        ax.set_xlabel('Epoch')
        ax.set_ylabel('Loss')
        ax.set_title('Standard Training History')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_predictions_vs_targets(self, fig, model, test_feat, test_targ, preprocessor, subplot_pos):
        """Plot predictions vs targets."""
        ax = fig.add_subplot(subplot_pos)
        
        # Get device from model parameters
        device = next(model.parameters()).device
        
        with torch.no_grad():
            test_tensor = torch.FloatTensor(test_feat).to(device)
            predictions = model(test_tensor).cpu().numpy()
        
        pred_orig = preprocessor.inverse_transform_targets(predictions).flatten()
        targ_orig = preprocessor.inverse_transform_targets(test_targ).flatten()
        
        valid_mask = np.isfinite(pred_orig) & np.isfinite(targ_orig)
        pred_clean = pred_orig[valid_mask]
        targ_clean = targ_orig[valid_mask]
        
        ax.scatter(targ_clean, pred_clean, alpha=0.5, s=1)
        
        min_val, max_val = min(targ_clean.min(), pred_clean.min()), max(targ_clean.max(), pred_clean.max())
        ax.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8)
        
        ax.set_xlabel('True VS (m/s)')
        ax.set_ylabel('Predicted VS (m/s)')
        ax.set_title('Predictions vs Targets')
        ax.grid(True, alpha=0.3)
    
    def _plot_physics_comparison(self, fig, test_feat, test_targ, preprocessor, subplot_pos):
        """Plot physics model comparison."""
        ax = fig.add_subplot(subplot_pos)
        
        # Get VP from features and convert to physics units
        vp_idx = 0  # Assuming VP is first feature
        vp_normalized = test_feat[:, :, vp_idx].flatten()
        
        # Denormalize VP
        vp_feature_2d = vp_normalized.reshape(-1, 1)
        dummy_features = np.zeros((len(vp_feature_2d), len(self.config['data']['features'])))
        dummy_features[:, vp_idx] = vp_feature_2d.flatten()
        
        vp_denorm = preprocessor.inverse_transform_features(dummy_features)[:, vp_idx]
        vp_kms = vp_denorm / 1000.0  # Convert to km/s
        
        # Get physics predictions
        physics_model = RockPhysicsFactory.create(self.config['rock_physics']['model_type'])
        vs_physics_kms = physics_model.predict(vp_kms)
        vs_physics_ms = vs_physics_kms * 1000.0  # Convert to m/s
        
        # Get true targets
        targ_orig = preprocessor.inverse_transform_targets(test_targ).flatten()
        
        valid_mask = np.isfinite(vs_physics_ms) & np.isfinite(targ_orig)
        phys_clean = vs_physics_ms[valid_mask]
        targ_clean = targ_orig[valid_mask]
        
        ax.scatter(targ_clean, phys_clean, alpha=0.5, s=1, c='orange')
        
        min_val, max_val = min(targ_clean.min(), phys_clean.min()), max(targ_clean.max(), phys_clean.max())
        ax.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8)
        
        ax.set_xlabel('True VS (m/s)')
        ax.set_ylabel('Physics VS (m/s)')
        ax.set_title('Physics Model vs Targets')
        ax.grid(True, alpha=0.3)
    
    def _plot_error_analysis(self, fig, model, test_feat, test_targ, preprocessor, subplot_pos):
        """Plot error analysis."""
        ax = fig.add_subplot(subplot_pos)
        
        # Get device from model parameters
        device = next(model.parameters()).device
        
        with torch.no_grad():
            test_tensor = torch.FloatTensor(test_feat).to(device)
            predictions = model(test_tensor).cpu().numpy()
        
        pred_orig = preprocessor.inverse_transform_targets(predictions).flatten()
        targ_orig = preprocessor.inverse_transform_targets(test_targ).flatten()
        
        valid_mask = np.isfinite(pred_orig) & np.isfinite(targ_orig)
        errors = (pred_orig - targ_orig)[valid_mask]
        
        ax.hist(errors, bins=50, alpha=0.7, density=True)
        ax.axvline(0, color='red', linestyle='--', alpha=0.8)
        ax.axvline(np.mean(errors), color='blue', linestyle='--', alpha=0.8, label=f'Mean: {np.mean(errors):.1f}')
        
        ax.set_xlabel('Prediction Error (m/s)')
        ax.set_ylabel('Density')
        ax.set_title('Error Distribution')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _create_extended_visualizations(self, processed_splits, model, preprocessor, evaluation_results, training_results):
        """Create extended, comprehensive visualizations (parity, residuals, sequences, coverage, physics comparison)."""
        # Unpack splits
        train_feat, val_feat, test_feat, train_targ, val_targ, test_targ = processed_splits

        # Concatenate for full-dataset views
        all_features = np.concatenate([train_feat, val_feat, test_feat], axis=0)
        all_targets = np.concatenate([train_targ, val_targ, test_targ], axis=0)

        # Split labels for coloring
        n_train, n_val, n_test = len(train_feat), len(val_feat), len(test_feat)
        split_labels = (['Train'] * n_train + ['Validation'] * n_val + ['Test'] * n_test)

        # Predictions on all data
        model.eval()
        with torch.no_grad():
            all_features_tensor = torch.FloatTensor(all_features).to(next(model.parameters()).device)
            all_predictions = model(all_features_tensor).cpu().numpy()

        # Inverse-transform to original scale
        all_predictions_orig = preprocessor.inverse_transform_targets(all_predictions)
        all_targets_orig = preprocessor.inverse_transform_targets(all_targets)

        # 1) Main plots (parity, residuals, training history, distributions)
        self._plot_tl_main_plots(all_predictions_orig, all_targets_orig, split_labels, evaluation_results, training_results)

        # 2) Detailed analysis (feature-target relationships and error distribution)
        self._plot_tl_detailed_analysis(all_features, all_targets, all_predictions_orig, all_targets_orig, preprocessor)

        # 3) Sequence analysis (several sequences with physics overlay)
        self._plot_tl_sequence_analysis(all_features, all_targets, all_predictions_orig, all_targets_orig, preprocessor)

        # 4) Data coverage heatmaps and stats
        self._plot_tl_data_coverage(all_features, all_targets, all_predictions_orig, all_targets_orig)

        # 5) Physics vs Model comparison on test set
        self._plot_tl_physics_comparison(test_feat, test_targ, model, preprocessor)

    def _plot_tl_main_plots(self, all_predictions_orig, all_targets_orig, split_labels, evaluation_results, training_results):
        plt.style.use('default')
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))

        pred_flat = all_predictions_orig.reshape(-1)
        target_flat = all_targets_orig.reshape(-1)

        split_flat = []
        for label in split_labels:
            split_flat.extend([label] * all_predictions_orig.shape[1])

        valid_mask = (~np.isnan(pred_flat) & ~np.isnan(target_flat) & ~np.isinf(pred_flat) & ~np.isinf(target_flat))
        pred_clean = pred_flat[valid_mask]
        target_clean = target_flat[valid_mask]
        split_clean = np.array(split_flat)[valid_mask]

        colors = {'Train': 'blue', 'Validation': 'orange', 'Test': 'red'}
        for split in ['Train', 'Validation', 'Test']:
            mask = split_clean == split
            if np.any(mask):
                axes[0, 0].scatter(target_clean[mask], pred_clean[mask], alpha=0.6, s=1, c=colors[split], label=split)
        min_val, max_val = target_clean.min(), target_clean.max()
        axes[0, 0].plot([min_val, max_val], [min_val, max_val], 'k--', lw=2, alpha=0.8)
        axes[0, 0].set_xlabel('Actual S-wave Velocity (m/s)')
        axes[0, 0].set_ylabel('Predicted S-wave Velocity (m/s)')
        axes[0, 0].set_title(f'Predictions vs Actual - Full Dataset\n(R² = {evaluation_results.get("r2", np.nan):.3f}, {len(pred_clean):,} points)')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        residuals = pred_clean - target_clean
        for split in ['Train', 'Validation', 'Test']:
            mask = split_clean == split
            if np.any(mask):
                axes[0, 1].scatter(target_clean[mask], residuals[mask], alpha=0.6, s=1, c=colors[split], label=split)
        axes[0, 1].axhline(y=0, color='k', linestyle='--', alpha=0.8)
        axes[0, 1].set_xlabel('Actual S-wave Velocity (m/s)')
        axes[0, 1].set_ylabel('Residuals (m/s)')
        axes[0, 1].set_title(f'Residual Analysis - Full Dataset\n(RMSE = {evaluation_results.get("rmse", np.nan):.3f} m/s)')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # Training history
        history = training_results.get('tl_helper').get_training_history() if 'tl_helper' in training_results else {}
        if history:
            if history.get('pretrain_history'):
                epochs = [h['epoch'] for h in history['pretrain_history']]
                axes[1, 0].plot(epochs, [h['train_loss'] for h in history['pretrain_history']], label='Pretrain Train Loss', alpha=0.8)
                axes[1, 0].plot(epochs, [h['val_loss'] for h in history['pretrain_history']], label='Pretrain Val Loss', alpha=0.8)
            if history.get('finetune_history'):
                epochs_ft = [h['epoch'] + (epochs[-1] if history.get('pretrain_history') else 0) for h in history['finetune_history']]
                axes[1, 0].plot(epochs_ft, [h['train_loss'] for h in history['finetune_history']], label='Finetune Train Loss', alpha=0.8)
                axes[1, 0].plot(epochs_ft, [h['val_loss'] for h in history['finetune_history']], label='Finetune Val Loss', alpha=0.8)
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('Loss')
        axes[1, 0].set_title('Training History (Pretrain + Finetune)')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # Distributions
        axes[1, 1].hist(target_clean, bins=50, alpha=0.7, color='skyblue', edgecolor='black', label='Actual')
        axes[1, 1].hist(pred_clean, bins=50, alpha=0.7, color='lightcoral', edgecolor='black', label='Predicted')
        axes[1, 1].set_xlabel('S-wave Velocity (m/s)')
        axes[1, 1].set_ylabel('Frequency')
        axes[1, 1].set_title(f'Data Distribution Comparison\n({len(pred_clean):,} total points)')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        out = self.output_dir / 'visualizations' / 'tl_main_results.png'
        plt.savefig(out, dpi=300, bbox_inches='tight')
        print(f"    TL main plots saved to '{out}'")
        plt.close(fig)

    def _plot_tl_detailed_analysis(self, all_features, all_targets, all_predictions_orig, all_targets_orig, preprocessor):
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        feature_names = self.config['data']['features']
        n_base = len(feature_names)
        base_features_norm = all_features[..., :n_base]
        base_features_orig = preprocessor.scalers['features'].inverse_transform(base_features_norm.reshape(-1, n_base)).reshape(base_features_norm.shape)

        features_flat = base_features_orig.reshape(-1, n_base)
        targets_flat = all_targets_orig.reshape(-1)
        predictions_flat = all_predictions_orig.reshape(-1)

        valid_mask = (~np.isnan(features_flat).any(axis=1) & ~np.isnan(targets_flat) & ~np.isnan(predictions_flat) & ~np.isinf(targets_flat) & ~np.isinf(predictions_flat))
        features_clean = features_flat[valid_mask]
        targets_clean = targets_flat[valid_mask]
        predictions_clean = predictions_flat[valid_mask]

        for i in range(min(4, len(feature_names))):
            row, col = i // 2, i % 2
            axes[row, col].scatter(features_clean[:, i], targets_clean, alpha=0.3, s=0.5, c='blue', label='Actual')
            axes[row, col].scatter(features_clean[:, i], predictions_clean, alpha=0.3, s=0.5, c='red', label='Predicted')
            axes[row, col].set_xlabel(f'{feature_names[i]}')
            axes[row, col].set_ylabel('S-wave Velocity (m/s)')
            axes[row, col].set_title(f'{feature_names[i]} vs S-wave Velocity\n({len(features_clean):,} points)')
            axes[row, col].legend()
            axes[row, col].grid(True, alpha=0.3)

        errors = predictions_clean - targets_clean
        axes[1, 2].hist(errors, bins=50, alpha=0.7, color='orange', edgecolor='black')
        axes[1, 2].axvline(x=0, color='red', linestyle='--', linewidth=2)
        axes[1, 2].set_xlabel('Prediction Error (m/s)')
        axes[1, 2].set_ylabel('Frequency')
        axes[1, 2].set_title(f'Error Distribution\nMean: {np.mean(errors):.3f}, Std: {np.std(errors):.3f}')
        axes[1, 2].grid(True, alpha=0.3)

        stats_text = (
            f'Statistics:\n'
            f'Mean Error: {np.mean(errors):.3f} m/s\n'
            f'Std Error: {np.std(errors):.3f} m/s\n'
            f'MAE: {np.mean(np.abs(errors)):.3f} m/s\n'
            f'RMSE: {np.sqrt(np.mean(errors**2)):.3f} m/s'
        )
        axes[1, 2].text(0.02, 0.98, stats_text, transform=axes[1, 2].transAxes, verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        plt.tight_layout()
        out = self.output_dir / 'visualizations' / 'tl_detailed_analysis.png'
        plt.savefig(out, dpi=300, bbox_inches='tight')
        print(f"    TL detailed analysis plots saved to '{out}'")
        plt.close(fig)

    def _plot_tl_sequence_analysis(self, all_features, all_targets, all_predictions_orig, all_targets_orig, preprocessor=None):
        fig, axes = plt.subplots(3, 2, figsize=(16, 18))
        n_sequences = len(all_features)
        sequence_length = all_features.shape[1]
        feature_names = self.config['data']['features']
        n_base = len(feature_names)
        base_features_norm = all_features[..., :n_base]
        if preprocessor is not None and hasattr(preprocessor, 'scalers') and 'features' in preprocessor.scalers:
            base_features_orig = preprocessor.scalers['features'].inverse_transform(base_features_norm.reshape(-1, n_base)).reshape(base_features_norm.shape)
        else:
            base_features_orig = base_features_norm.copy()
        vp_feature_idx = feature_names.index('P-WAVE') if 'P-WAVE' in feature_names else 0
        physics_model = RockPhysicsFactory.create('mudrock_line')

        sequence_indices = [0, n_sequences // 4, n_sequences // 2, 3 * n_sequences // 4, n_sequences - 1, np.random.randint(0, n_sequences)]
        sequence_labels = ['First', 'Quarter', 'Middle', 'Three-Quarter', 'Last', 'Random']

        for i, (seq_idx, label) in enumerate(zip(sequence_indices, sequence_labels)):
            row, col = i // 2, i % 2
            if seq_idx >= n_sequences:
                seq_idx = n_sequences - 1
            actual_seq = all_targets_orig[seq_idx, :, 0]
            pred_seq = all_predictions_orig[seq_idx, :, 0]
            vp_seq = base_features_orig[seq_idx, :, vp_feature_idx]
            if np.nanmax(vp_seq) <= 1.5:
                vp_min, vp_max = 2000.0, 6000.0
                vp_seq_mps = vp_seq * (vp_max - vp_min) + vp_min
            else:
                vp_seq_mps = vp_seq
            physics_seq = physics_model.predict(vp_seq_mps / 1000.0) * 1000.0
            depth_indices = np.arange(len(actual_seq)) * 0.5

            axes[row, col].plot(depth_indices, actual_seq, color='black', linestyle='-', linewidth=2, alpha=0.8, label='Actual')
            axes[row, col].plot(depth_indices, pred_seq, color='red', linestyle='--', linewidth=2, alpha=0.8, label='BiGRU')
            axes[row, col].plot(depth_indices, physics_seq, color='blue', linestyle='-.', linewidth=2, alpha=0.8, label='Physics')

            seq_rmse_bigru = np.sqrt(np.mean((pred_seq - actual_seq) ** 2))
            seq_corr_bigru = np.corrcoef(actual_seq, pred_seq)[0, 1] if len(actual_seq) > 1 else 0
            seq_rmse_phys = np.sqrt(np.mean((physics_seq - actual_seq) ** 2))
            seq_corr_phys = np.corrcoef(actual_seq, physics_seq)[0, 1] if len(actual_seq) > 1 else 0
            seq_r2_bigru = seq_corr_bigru ** 2 if not np.isnan(seq_corr_bigru) else 0.0
            seq_r2_phys = seq_corr_phys ** 2 if not np.isnan(seq_corr_phys) else 0.0

            axes[row, col].set_xlabel('Relative Depth (m)')
            axes[row, col].set_ylabel('S-wave Velocity (m/s)')
            axes[row, col].set_title(f"{label} Sequence | BiGRU RMSE: {seq_rmse_bigru:.2f}, R²: {seq_r2_bigru:.3f} | Phys RMSE: {seq_rmse_phys:.2f}, R²: {seq_r2_phys:.3f}")
            axes[row, col].legend()
            axes[row, col].grid(True, alpha=0.3)

        plt.tight_layout()
        out = self.output_dir / 'visualizations' / 'tl_sequence_analysis.png'
        plt.savefig(out, dpi=300, bbox_inches='tight')
        print(f"    TL sequence analysis plots saved to '{out}'")
        plt.close(fig)

    def _plot_tl_data_coverage(self, all_features, all_targets, all_predictions_orig, all_targets_orig):
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        n_sequences = len(all_features)
        sequence_length = all_features.shape[1]
        sequence_indices = np.arange(n_sequences)

        targets_2d = all_targets_orig[:, :, 0]
        im1 = axes[0, 0].imshow(targets_2d.T, aspect='auto', cmap='viridis', interpolation='nearest')
        axes[0, 0].set_xlabel('Sequence Index')
        axes[0, 0].set_ylabel('Depth Index within Sequence')
        axes[0, 0].set_title(f'Data Coverage Heatmap - Actual VS\n({n_sequences}×{sequence_length})')
        plt.colorbar(im1, ax=axes[0, 0], label='S-wave Velocity (m/s)')

        predictions_2d = all_predictions_orig[:, :, 0]
        im2 = axes[0, 1].imshow(predictions_2d.T, aspect='auto', cmap='plasma', interpolation='nearest')
        axes[0, 1].set_xlabel('Sequence Index')
        axes[0, 1].set_ylabel('Depth Index within Sequence')
        axes[0, 1].set_title('Prediction Coverage Heatmap - Predicted VS')
        plt.colorbar(im2, ax=axes[0, 1], label='S-wave Velocity (m/s)')

        seq_means_actual = np.mean(targets_2d, axis=1)
        seq_means_pred = np.mean(predictions_2d, axis=1)
        seq_stds_actual = np.std(targets_2d, axis=1)
        seq_stds_pred = np.std(predictions_2d, axis=1)

        axes[1, 0].plot(sequence_indices, seq_means_actual, 'b-', alpha=0.7, label='Actual Mean', linewidth=1)
        axes[1, 0].plot(sequence_indices, seq_means_pred, 'r-', alpha=0.7, label='Predicted Mean', linewidth=1)
        axes[1, 0].fill_between(sequence_indices, seq_means_actual - seq_stds_actual, seq_means_actual + seq_stds_actual, alpha=0.2, color='blue', label='Actual ±1σ')
        axes[1, 0].fill_between(sequence_indices, seq_means_pred - seq_stds_pred, seq_means_pred + seq_stds_pred, alpha=0.2, color='red', label='Predicted ±1σ')
        axes[1, 0].set_xlabel('Sequence Index')
        axes[1, 0].set_ylabel('S-wave Velocity (m/s)')
        axes[1, 0].set_title('Sequence-wise Statistics')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        total_possible_points = n_sequences * sequence_length
        valid_actual = ~np.isnan(targets_2d)
        valid_pred = ~np.isnan(predictions_2d)
        utilization_data = [valid_actual.sum(), valid_pred.sum(), total_possible_points]
        labels = ['Valid Actual', 'Valid Predicted', 'Total Possible']
        colors = ['skyblue', 'lightcoral', 'lightgray']
        bars = axes[1, 1].bar(labels, utilization_data, color=colors, alpha=0.8, edgecolor='black')
        axes[1, 1].set_ylabel('Number of Data Points')
        axes[1, 1].set_title('Data Utilization Summary')
        axes[1, 1].grid(True, alpha=0.3, axis='y')
        for bar, value in zip(bars, utilization_data):
            height = bar.get_height()
            axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + max(1.0, height*0.01), f'{value:,}', ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()
        out = self.output_dir / 'visualizations' / 'tl_data_coverage.png'
        plt.savefig(out, dpi=300, bbox_inches='tight')
        print(f"    TL data coverage plots saved to '{out}'")
        plt.close(fig)

    def _plot_tl_physics_comparison(self, test_feat, test_targ, model, preprocessor):
        try:
            # Predict with model on test
            model.eval()
            with torch.no_grad():
                test_tensor = torch.FloatTensor(test_feat).to(next(model.parameters()).device)
                bigru_pred = model(test_tensor).cpu().numpy()
            bigru_pred_orig = preprocessor.inverse_transform_targets(bigru_pred)
            test_targ_orig = preprocessor.inverse_transform_targets(test_targ)

            # Physics predictions from VP (denormalized)
            vp_idx = 0  # default fallback
            feature_names = self.config['data']['features']
            if 'P-WAVE' in feature_names:
                vp_idx = feature_names.index('P-WAVE')
            vp_norm = test_feat[:, :, vp_idx]
            vp_denorm = preprocessor.inverse_transform_features(np.expand_dims(vp_norm, -1), [vp_idx]).squeeze(-1)
            vp_kms = vp_denorm / 1000.0
            physics_model = RockPhysicsFactory.create(self.config['rock_physics']['model_type'], **self.config['rock_physics'].get('params', {}))
            vs_physics_ms = physics_model.predict(vp_kms) * 1000.0

            # Flatten for comparison
            targets_flat = test_targ_orig.reshape(-1)
            bigru_flat = bigru_pred_orig.reshape(-1)
            physics_flat = vs_physics_ms.reshape(-1)

            # Clean
            valid_mask = (~np.isnan(targets_flat) & ~np.isnan(bigru_flat) & ~np.isnan(physics_flat) & ~np.isinf(targets_flat) & ~np.isinf(bigru_flat) & ~np.isinf(physics_flat))
            targets_clean = targets_flat[valid_mask]
            bigru_clean = bigru_flat[valid_mask]
            physics_clean = physics_flat[valid_mask]

            # Main comparison plot
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle('Physics vs BiGRU vs Real (Test Set)', fontsize=16, fontweight='bold')

            axes[0, 0].scatter(targets_clean, bigru_clean, alpha=0.6, s=2, c='red', label='BiGRU vs Real')
            axes[0, 0].scatter(targets_clean, physics_clean, alpha=0.6, s=2, c='blue', label='Physics vs Real')
            min_val, max_val = targets_clean.min(), targets_clean.max()
            axes[0, 0].plot([min_val, max_val], [min_val, max_val], 'k--', lw=2, alpha=0.8)
            axes[0, 0].set_xlabel('Real VS (m/s)')
            axes[0, 0].set_ylabel('Predicted VS (m/s)')
            axes[0, 0].set_title('Predictions vs Real Data (Test)')
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)

            axes[0, 1].scatter(physics_clean, bigru_clean, alpha=0.6, s=2, c='green')
            min_val2, max_val2 = min(physics_clean.min(), bigru_clean.min()), max(physics_clean.max(), bigru_clean.max())
            axes[0, 1].plot([min_val2, max_val2], [min_val2, max_val2], 'k--', lw=2, alpha=0.8)
            axes[0, 1].set_xlabel('Physics VS (m/s)')
            axes[0, 1].set_ylabel('BiGRU VS (m/s)')
            axes[0, 1].set_title('BiGRU vs Physics (Test)')
            axes[0, 1].grid(True, alpha=0.3)

            # Metrics box
            axes[1, 0].axis('off')
            bigru_rmse = np.sqrt(np.mean((bigru_clean - targets_clean) ** 2))
            bigru_mae = np.mean(np.abs(bigru_clean - targets_clean))
            bigru_corr = np.corrcoef(bigru_clean, targets_clean)[0, 1]
            bigru_r2 = bigru_corr ** 2
            phys_rmse = np.sqrt(np.mean((physics_clean - targets_clean) ** 2))
            phys_mae = np.mean(np.abs(physics_clean - targets_clean))
            phys_corr = np.corrcoef(physics_clean, targets_clean)[0, 1]
            phys_r2 = phys_corr ** 2
            text = (
                f'BiGRU (Test): RMSE={bigru_rmse:.2f} m/s, MAE={bigru_mae:.2f} m/s, R²={bigru_r2:.4f}, Corr={bigru_corr:.4f}\n'
                f'Physics(Test): RMSE={phys_rmse:.2f} m/s, MAE={phys_mae:.2f} m/s, R²={phys_r2:.4f}, Corr={phys_corr:.4f}'
            )
            axes[1, 0].text(0.05, 0.95, text, transform=axes[1, 0].transAxes, va='top', fontsize=10, fontfamily='monospace', bbox=dict(boxstyle='round,pad=0.5', facecolor='lightyellow', alpha=0.9, edgecolor='orange'))

            # VP-VS relationships
            axes[1, 1].axis('off')
            # Could add VP-VS plots here with available VP arrays if desired

            plt.tight_layout()
            out = self.output_dir / 'visualizations' / 'tl_physics_comparison.png'
            plt.savefig(out, dpi=300, bbox_inches='tight')
            print(f"    TL physics comparison plot saved to '{out}'")
            plt.close(fig)
        except Exception as e:
            print(f"    Warning: TL physics comparison failed: {e}")

    def _create_well_specific_visualizations(self, data_splits, model, preprocessor, evaluation_results):
        """Create individual well plots and a wells comparison grid (adapted from main pipeline)."""
        try:
            print("  Creating well-specific visualizations...")

            if not hasattr(self, 'well_data_individual') or not self.well_data_individual:
                print("  Warning: No individual well data available for well-specific plots")
                return

            # Sort wells by name
            sorted_well_names = sorted(self.well_data_individual.keys())
            print(f"  Found {len(sorted_well_names)} wells: {', '.join(sorted_well_names)}")

            # Create per-well plots
            self._create_individual_well_plots(sorted_well_names, model, preprocessor)
            # Create comparison grid
            self._create_wells_comparison_plot(sorted_well_names, model, preprocessor)

            print("  Well-specific visualizations completed.")
        except Exception as e:
            print(f"  Error creating well-specific visualizations: {e}")
            import traceback
            traceback.print_exc()

    def _create_individual_well_plots(self, sorted_well_names, model, preprocessor):
        """Create individual plots for each well (predictions vs actual, log display, residuals, stats)."""
        print("    Creating individual well plots...")

        for well_name in sorted_well_names:
            try:
                well_data = self.well_data_individual[well_name]
                features = well_data['features']
                targets = well_data['targets']

                # Apply configured log transforms (if any)
                feature_names = self.config['data']['features']
                if 'apply_log_transform' in self.config['data'] and len(self.config['data']['apply_log_transform']) > 0:
                    features_df = pd.DataFrame(features, columns=feature_names)
                    features_df = apply_log_transforms(features_df, self.config)
                    features = features_df.values

                # Create sequences for this well using same params
                processor = WellLogDataProcessor(self.config)
                seq_features, seq_targets = processor.create_sequences(
                    features, targets,
                    sequence_length=self.config['data']['sequence_length'],
                    stride=self.config['data']['sequence_stride']
                )

                if len(seq_features) == 0:
                    print(f"      {well_name}: No sequences could be created, skipping...")
                    continue

                # Normalize with pre-fitted preprocessor
                seq_features_norm, seq_targets_norm = preprocessor.transform(seq_features, seq_targets)

                # Predict
                model.eval()
                with torch.no_grad():
                    features_tensor = torch.FloatTensor(seq_features_norm).to(next(model.parameters()).device)
                    predictions = model(features_tensor).cpu().numpy()

                # Inverse transform predictions/targets to original units
                predictions_orig = preprocessor.inverse_transform_targets(predictions)
                targets_orig = preprocessor.inverse_transform_targets(seq_targets_norm)

                # Plot single well
                self._plot_single_well(well_name, well_data, seq_features_norm, targets_orig, predictions_orig, preprocessor)

                print(f"      {well_name}: Plot created successfully")
            except Exception as e:
                print(f"      {well_name}: Error creating plot - {e}")

    def _plot_single_well(self, well_name, well_data, features, targets_orig, predictions_orig, preprocessor):
        """Create a comprehensive plot for a single well (mirroring main pipeline style)."""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))

        clean_well_name = well_name.replace('.las', '').replace('_', ' ')
        fig.suptitle(f'Individual Well Analysis: {clean_well_name}', fontsize=18, fontweight='bold', color='darkblue')

        targets_flat = targets_orig.reshape(-1)
        predictions_flat = predictions_orig.reshape(-1)

        valid_mask = np.isfinite(targets_flat) & np.isfinite(predictions_flat)
        targets_clean = targets_flat[valid_mask]
        predictions_clean = predictions_flat[valid_mask]

        if len(targets_clean) == 0:
            for ax in axes.flat:
                ax.text(0.5, 0.5, f'No Valid Data Available\nfor {clean_well_name}', ha='center', va='center', transform=ax.transAxes, fontsize=14, bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))
                ax.set_title('No Data')
            plt.tight_layout()
            safe_well_name = well_name.replace('.', '_').replace(' ', '_')
            well_plot_path = self.output_dir / "visualizations" / f"well_specific_{safe_well_name}.png"
            plt.savefig(well_plot_path, dpi=300, bbox_inches='tight')
            plt.close(fig)
            return

        # Parity plot
        axes[0, 0].scatter(targets_clean, predictions_clean, alpha=0.7, s=3, c='blue', edgecolors='navy', linewidth=0.1)
        min_val, max_val = targets_clean.min(), targets_clean.max()
        axes[0, 0].plot([min_val, max_val], [min_val, max_val], 'r--', lw=2, alpha=0.8, label='Perfect Prediction')
        axes[0, 0].set_xlabel('Actual S-wave Velocity (m/s)', fontweight='bold')
        axes[0, 0].set_ylabel('Predicted S-wave Velocity (m/s)', fontweight='bold')

        rmse = np.sqrt(np.mean((predictions_clean - targets_clean) ** 2))
        corr = np.corrcoef(targets_clean, predictions_clean)[0, 1]
        r2 = corr ** 2
        mae = np.mean(np.abs(predictions_clean - targets_clean))

        axes[0, 0].set_title(f'{clean_well_name} - Predictions vs Actual\nRMSE: {rmse:.3f} m/s | R²: {r2:.3f} | Correlation: {corr:.3f}', fontweight='bold')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # Well log display
        depth_indices = np.arange(len(targets_clean)) * 0.5
        depth_start = well_data.get('depth_range', (0.0, 0.0))[0]
        actual_depths = depth_start + depth_indices

        axes[0, 1].plot(targets_clean, actual_depths, 'b-', linewidth=2, alpha=0.8, label='Actual S-wave')
        axes[0, 1].plot(predictions_clean, actual_depths, 'r--', linewidth=2, alpha=0.8, label='Predicted S-wave')
        axes[0, 1].set_xlabel('S-wave Velocity (m/s)', fontweight='bold')
        axes[0, 1].set_ylabel('Depth (m)', fontweight='bold')
        depth_range = well_data.get('depth_range', (0.0, 0.0))
        axes[0, 1].set_title(f'{clean_well_name} - Well Log Display\nDepth Range: {depth_range[0]:.0f}-{depth_range[1]:.0f}m | {len(targets_clean):,} points', fontweight='bold')
        axes[0, 1].legend(loc='best')
        axes[0, 1].grid(True, alpha=0.3)
        axes[0, 1].invert_yaxis()

        axes[0, 1].text(0.02, 0.98, f'Well: {clean_well_name}', transform=axes[0, 1].transAxes, va='top', fontweight='bold', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.7))

        # Residuals
        residuals = predictions_clean - targets_clean
        axes[1, 0].scatter(targets_clean, residuals, alpha=0.7, s=3, c='green', edgecolors='darkgreen', linewidth=0.1)
        axes[1, 0].axhline(y=0, color='r', linestyle='--', alpha=0.8, linewidth=2, label='Zero Error')
        axes[1, 0].set_xlabel('Actual S-wave Velocity (m/s)', fontweight='bold')
        axes[1, 0].set_ylabel('Residuals (m/s)', fontweight='bold')
        mean_resid = np.mean(residuals)
        std_resid = np.std(residuals)
        axes[1, 0].set_title(f'{clean_well_name} - Residual Analysis\nMean: {mean_resid:.3f} m/s | Std: {std_resid:.3f} m/s | MAE: {mae:.3f} m/s', fontweight='bold')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        axes[1, 0].axhline(y=mean_resid, color='orange', linestyle=':', alpha=0.7)
        axes[1, 0].axhline(y=mean_resid + std_resid, color='orange', linestyle=':', alpha=0.5)
        axes[1, 0].axhline(y=mean_resid - std_resid, color='orange', linestyle=':', alpha=0.5)

        # Stats panel
        axes[1, 1].axis('off')
        well_info_text = (
            f'WELL INFORMATION\n'
            f'{"="*25}\n'
            f'Well Name: {clean_well_name}\n'
            f'File: {os.path.basename(well_data.get("file_path", well_name))}\n'
            f'Depth Range: {depth_range[0]:.0f} - {depth_range[1]:.0f} m\n'
            f'Data Points: {len(targets_clean):,}\n\n'
        )
        stats_text = (
            f'PERFORMANCE METRICS\n'
            f'{"="*25}\n'
            f'RMSE: {rmse:.4f} m/s\n'
            f'R² Score: {r2:.4f}\n'
            f'Correlation: {corr:.4f}\n'
            f'MAE: {mae:.4f} m/s\n'
            f'Mean Residual: {mean_resid:.4f} m/s\n'
            f'Std Residual: {std_resid:.4f} m/s\n'
        )
        range_text = (
            f'DATA RANGES\n'
            f'{"="*25}\n'
            f'Actual VS: min={targets_clean.min():.0f}, max={targets_clean.max():.0f}, mean={np.mean(targets_clean):.0f} m/s\n'
            f'Pred VS:  min={predictions_clean.min():.0f}, max={predictions_clean.max():.0f}, mean={np.mean(predictions_clean):.0f} m/s\n'
        )
        full_text = well_info_text + stats_text + range_text
        axes[1, 1].text(0.05, 0.95, full_text, transform=axes[1, 1].transAxes, va='top', fontsize=9, fontfamily='monospace', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.9, edgecolor='navy'))

        plt.tight_layout()
        safe_well_name = well_name.replace('.', '_').replace(' ', '_')
        well_plot_path = self.output_dir / "visualizations" / f"well_specific_{safe_well_name}.png"
        plt.savefig(well_plot_path, dpi=300, bbox_inches='tight')
        plt.close(fig)

    def _create_wells_comparison_plot(self, sorted_well_names, model, preprocessor):
        """Create a comparison plot showing all wells in a grid."""
        print("    Creating wells comparison plot...")

        try:
            n_wells = len(sorted_well_names)
            n_cols = min(3, n_wells)
            n_rows = (n_wells + n_cols - 1) // n_cols

            fig, axes = plt.subplots(n_rows, n_cols, figsize=(6*n_cols, 8*n_rows))
            fig.suptitle('Well Comparison - S-wave Velocity Predictions (Sorted by Well Name)', fontsize=16, fontweight='bold')
            axes = np.atleast_1d(axes).ravel()

            for idx, well_name in enumerate(sorted_well_names):
                ax = axes[idx]
                try:
                    well_data = self.well_data_individual[well_name]
                    features = well_data['features']
                    targets = well_data['targets']

                    processor = WellLogDataProcessor(self.config)
                    seq_features, seq_targets = processor.create_sequences(
                        features, targets,
                        sequence_length=self.config['data']['sequence_length'],
                        stride=self.config['data']['sequence_stride']
                    )
                    if len(seq_features) == 0:
                        ax.text(0.5, 0.5, f'{well_name}\n(No sequences)', ha='center', va='center', transform=ax.transAxes)
                        ax.set_title(well_name)
                        continue

                    # Apply same log transforms if configured
                    feature_names = self.config['data']['features']
                    if 'apply_log_transform' in self.config['data'] and len(self.config['data']['apply_log_transform']) > 0:
                        seq_features_df = pd.DataFrame(seq_features.reshape(-1, seq_features.shape[-1]), columns=feature_names)
                        seq_features_df = apply_log_transforms(seq_features_df, self.config)
                        seq_features = seq_features_df.values.reshape(seq_features.shape)

                    # Normalize and predict
                    seq_features_norm, seq_targets_norm = preprocessor.transform(seq_features, seq_targets)
                    model.eval()
                    with torch.no_grad():
                        features_tensor = torch.FloatTensor(seq_features_norm).to(next(model.parameters()).device)
                        predictions = model(features_tensor).cpu().numpy()

                    predictions_orig = preprocessor.inverse_transform_targets(predictions)
                    targets_orig = preprocessor.inverse_transform_targets(seq_targets_norm)

                    targets_flat = targets_orig.reshape(-1)
                    predictions_flat = predictions_orig.reshape(-1)
                    valid_mask = np.isfinite(targets_flat) & np.isfinite(predictions_flat)
                    targets_clean = targets_flat[valid_mask]
                    predictions_clean = predictions_flat[valid_mask]

                    depth_indices = np.arange(len(targets_clean)) * 0.5
                    depth_start = well_data.get('depth_range', (0.0, 0.0))[0]
                    actual_depths = depth_start + depth_indices

                    ax.plot(targets_clean, actual_depths, 'b-', linewidth=2, alpha=0.8, label='Actual')
                    ax.plot(predictions_clean, actual_depths, 'r--', linewidth=2, alpha=0.8, label='Predicted')

                    corr = np.corrcoef(targets_clean, predictions_clean)[0, 1] if len(targets_clean) > 1 else 0.0
                    r2 = corr ** 2
                    rmse = np.sqrt(np.mean((predictions_clean - targets_clean) ** 2))

                    ax.set_xlabel('S-wave Velocity (m/s)', fontweight='bold')
                    ax.set_ylabel('Depth (m)', fontweight='bold')
                    clean_well_name = well_name.replace('.las', '').replace('_', ' ')
                    ax.set_title(f'{clean_well_name}\nRMSE: {rmse:.3f} | R²: {r2:.3f} | {len(targets_clean):,} points', fontweight='bold', fontsize=10)
                    ax.legend(fontsize=8, loc='best')
                    ax.grid(True, alpha=0.3)
                    ax.invert_yaxis()
                except Exception as e:
                    clean_well_name = well_name.replace('.las', '').replace('_', ' ')
                    ax.text(0.5, 0.5, f'{clean_well_name}\n(Error: {str(e)[:30]}...)', ha='center', va='center', transform=ax.transAxes, bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))
                    ax.set_title(clean_well_name, fontweight='bold')
                    print(f"      Error processing {well_name}: {e}")

            for idx in range(n_wells, n_rows * n_cols):
                axes[idx].set_visible(False)

            plt.tight_layout()
            comparison_path = self.output_dir / "visualizations" / "wells_comparison_sorted.png"
            plt.savefig(comparison_path, dpi=300, bbox_inches='tight')
            print(f"    Wells comparison plot saved to '{comparison_path}'")
            plt.close(fig)
        except Exception as e:
            print(f"    Error creating wells comparison plot: {e}")
            import traceback
            traceback.print_exc()

    def _print_summary(self):
        """Print test summary."""
        if 'evaluation' in self.results:
            metrics = self.results['evaluation']
            training = self.results['training']
            
            print(f"🎯 Final Results:")
            print(f"   Training Method: {training['method'].title()}")
            print(f"   RMSE: {metrics['rmse']:.2f} m/s")
            print(f"   R²: {metrics['r2']:.4f}")
            print(f"   Correlation: {metrics['correlation']:.4f}")
            
            if training['method'] == 'transfer_learning':
                print(f"   Pretrain Loss: {training['pretrain_loss']:.4f}")
                print(f"   Finetune Loss: {training['finetune_loss']:.4f}")


def main():
    """Main function to run the enhanced pipeline."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Enhanced LAS ML Pipeline with Transfer Learning')
    parser.add_argument('--config', default='configs/default_config.yaml', help='Config file path')
    parser.add_argument('--output-dir', default='output', help='Output directory')
    parser.add_argument('--no-transfer-learning', action='store_true', help='Disable transfer learning')
    parser.add_argument('--compare-methods', action='store_true', help='Compare both methods')
    parser.add_argument('--blind-test', action='store_true', help='Enable blind test protocol (train B-L-15, test others)')
    parser.add_argument('--seed', type=int, default=42, help='Random seed for reproducibility (default: 42)')
    
    args = parser.parse_args()
    
    if args.compare_methods:
        # Run both methods for comparison
        print("🔄 Running comparison between standard and transfer learning methods...")
        
        # Standard training
        print("\n" + "="*40 + " STANDARD TRAINING " + "="*40)
        pipeline_std = LASTransferLearningPipelineTest(
            config_path=args.config,
            output_dir=args.output_dir + "_standard",
            enable_transfer_learning=False,
            enable_blind_test=args.blind_test
        )
        results_std = pipeline_std.run_complete_test()
        
        # Transfer learning
        print("\n" + "="*40 + " TRANSFER LEARNING " + "="*40)
        pipeline_tl = LASTransferLearningPipelineTest(
            config_path=args.config,
            output_dir=args.output_dir + "_transfer",
            enable_transfer_learning=True,
            enable_blind_test=args.blind_test
        )
        results_tl = pipeline_tl.run_complete_test()
        
        # Comparison summary
        print("\n" + "="*80)
        print("📊 METHOD COMPARISON SUMMARY")
        print("="*80)
        
        if 'evaluation' in results_std and 'evaluation' in results_tl:
            std_metrics = results_std['evaluation']
            tl_metrics = results_tl['evaluation']
            
            print(f"{'Metric':<15} {'Standard':<12} {'Transfer':<12} {'Improvement'}")
            print("-" * 55)
            print(f"{'RMSE:':<15} {std_metrics['rmse']:<12.2f} {tl_metrics['rmse']:<12.2f} {((std_metrics['rmse'] - tl_metrics['rmse'])/std_metrics['rmse']*100):+6.1f}%")
            print(f"{'R²:':<15} {std_metrics['r2']:<12.4f} {tl_metrics['r2']:<12.4f} {((tl_metrics['r2'] - std_metrics['r2'])/std_metrics['r2']*100):+6.1f}%")
            print(f"{'Correlation:':<15} {std_metrics['correlation']:<12.4f} {tl_metrics['correlation']:<12.4f} {((tl_metrics['correlation'] - std_metrics['correlation'])/std_metrics['correlation']*100):+6.1f}%")
        
    else:
        # Run single method
        enable_tl = not args.no_transfer_learning
        pipeline = LASTransferLearningPipelineTest(
            config_path=args.config,
            output_dir=args.output_dir,
            enable_transfer_learning=enable_tl,
            enable_blind_test=args.blind_test
        )
        results = pipeline.run_complete_test()

    def _set_deterministic_seed(self, seed: int = 42):
        """Set deterministic seed for reproducibility."""
        import random
        import torch

        # Set seeds for reproducibility
        random.seed(seed)
        np.random.seed(seed)
        torch.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)

        # Make cudNN deterministic (slower but reproducible)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False

        print(f"   Deterministic seed set: {seed}")

    def _setup_model(self, input_dim: int, preprocessor):
        """Setup model, trainer, and loss function."""
        self.config['model']['params']['input_dim'] = input_dim

        rock_physics_model = RockPhysicsFactory.create(
            self.config['rock_physics']['model_type'],
            **self.config['rock_physics']['params']
        )

        strategy = PhysicsGuidanceStrategy(self.config['training']['strategy'])
        strategy_handler = StrategyHandler(strategy, rock_physics_model)

        model = BiGRU(**self.config['model']['params'])

        trainer = PhysicsGuidedTrainer(
            model=model,
            rock_physics_model=rock_physics_model,
            strategy_handler=strategy_handler,
            config=self.config,
            preprocessor=preprocessor
        )

        loss_fn = strategy_handler.get_loss_function()

        print(f"  Model parameters: {sum(p.numel() for p in model.parameters()):,}")
        print(f"  Rock physics model: {rock_physics_model.name}")

        return model, trainer, loss_fn

    def _train_with_transfer_learning_blind_test(self, model, trainer, loss_fn,
                                                train_features, train_targets,
                                                val_features, val_targets):
        """Train with transfer learning using blind test data."""
        from torch.utils.data import DataLoader, TensorDataset
        from training.transfer_learning import TransferLearningHelper

        # Create data loaders
        train_loader = DataLoader(
            TensorDataset(torch.FloatTensor(train_features), torch.FloatTensor(train_targets)),
            batch_size=self.config['training']['batch_size'], shuffle=True
        )
        val_loader = DataLoader(
            TensorDataset(torch.FloatTensor(val_features), torch.FloatTensor(val_targets)),
            batch_size=self.config['training']['batch_size'], shuffle=False
        )

        # Create transfer learning helper
        tl_helper = TransferLearningHelper(self.config, trainer, model, loss_fn)

        # Run transfer learning
        results = tl_helper.run_transfer_learning(train_loader, val_loader, preprocessor=trainer.preprocessor)

        # Plot training history
        try:
            tl_helper.plot_training_history(str(self.blind_test.blind_test_dir / "visualizations" / "transfer_learning_history.png"))
        except:
            print("  Could not create training history plot")

        results['method'] = 'transfer_learning_blind_test'
        results['tl_helper'] = tl_helper

        return results

    def _train_standard_blind_test(self, model, trainer, loss_fn,
                                  train_features, train_targets,
                                  val_features, val_targets):
        """Train with standard method using blind test data."""
        import torch
        from torch.utils.data import DataLoader, TensorDataset
        from tqdm import tqdm

        train_loader = DataLoader(
            TensorDataset(torch.FloatTensor(train_features), torch.FloatTensor(train_targets)),
            batch_size=self.config['training']['batch_size'], shuffle=True
        )
        val_loader = DataLoader(
            TensorDataset(torch.FloatTensor(val_features), torch.FloatTensor(val_targets)),
            batch_size=self.config['training']['batch_size'], shuffle=False
        )

        optimizer = torch.optim.Adam(model.parameters(), lr=self.config['training']['learning_rate'])

        history = {'train_loss': [], 'val_loss': [], 'val_rmse': [], 'val_correlation': []}
        best_val_loss = float('inf')

        for epoch in tqdm(range(self.config['training']['epochs']), desc="Training"):
            train_loss = trainer.train_epoch(train_loader, optimizer, loss_fn)
            val_metrics = trainer.evaluate(val_loader, torch.nn.MSELoss())

            history['train_loss'].append(train_loss)
            history['val_loss'].append(val_metrics['loss'])
            history['val_rmse'].append(val_metrics['rmse'])
            history['val_correlation'].append(val_metrics['correlation'])

            if val_metrics['loss'] < best_val_loss:
                best_val_loss = val_metrics['loss']
                torch.save(model.state_dict(), self.blind_test.blind_test_dir / "models" / "best_standard_model.pth")

        # Load best model
        model.load_state_dict(torch.load(self.blind_test.blind_test_dir / "models" / "best_standard_model.pth"))

        return {
            'method': 'standard_blind_test',
            'best_val_loss': best_val_loss,
            'history': history
        }


if __name__ == "__main__":
    main()