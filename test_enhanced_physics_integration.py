#!/usr/bin/env python3
"""
Test script to verify the enhanced physics integration improvements
in the test_las_transfer_learning_pipeline.py
"""

import sys
import os
import numpy as np
import torch

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_physics_integration_improvements():
    """Test all three physics integration improvements."""
    print("="*80)
    print("🔬 TESTING ENHANCED PHYSICS INTEGRATION IMPROVEMENTS")
    print("="*80)
    
    try:
        # Import required modules
        from utils.physics_coupling import PhysicsCouplingManager
        from models.rock_physics import RockPhysicsFactory
        from data.preprocessing import WellLogPreprocessor
        from training.strategies import PhysicsGuidanceStrategy, StrategyHandler
        from training.trainer import PhysicsGuidedTrainer
        from models.neural_networks import BiGRU
        
        print("✅ All imports successful")
        
        # Test configuration
        config = {
            'data': {
                'features': ['P-WAVE', 'RHOB', 'PHIE', 'RT'],
                'vp_feature_name': 'P-WAVE'
            },
            'physics_coupling': {'conversion_factor': 0.001},
            'model': {'params': {'input_dim': 4, 'hidden_dim': 16, 'output_dim': 1, 'num_layers': 1, 'dropout': 0.1}},
            'training': {'strategy': 'pseudolabels'}
        }
        
        # Create test data
        features = np.random.randn(100, 20, 4)  # [B, T, F]
        targets = np.random.randn(100, 20, 1)   # [B, T, 1]
        feature_names = ['P-WAVE', 'RHOB', 'PHIE', 'RT']
        
        print(f"\n📊 Test data: features {features.shape}, targets {targets.shape}")
        
        # Test Step 1: Centralized Physics Coupling
        print("\n1️⃣ Testing Step 1: Centralized Physics Coupling")
        
        rock_physics_model = RockPhysicsFactory.create('mudrock_line')
        physics_coupling = PhysicsCouplingManager(config, 0, rock_physics_model)
        
        # Test physics predictions
        physics_pred = physics_coupling.compute_physics_predictions(features)
        print(f"   ✅ Physics predictions shape: {physics_pred.shape}")
        print(f"   ✅ Physics coupling manager created and tested")
        
        # Test Step 2: Sequence-Aware Pseudolabel Augmentation
        print("\n2️⃣ Testing Step 2: Enhanced Pseudolabel Augmentation")
        
        preprocessor = WellLogPreprocessor(normalization='standard')
        
        # Test the new fit_with_pseudolabels method
        preprocessor.fit_with_pseudolabels(features, targets, physics_coupling, feature_names)
        
        print(f"   ✅ Original features: {len(feature_names)}")
        print(f"   ✅ Augmented features: {len(preprocessor.feature_names)}")
        print(f"   ✅ Feature names: {preprocessor.feature_names}")
        
        # Test augmentation method (note: this requires a preprocessor fitted without pseudolabels)
        # Create a separate preprocessor for testing augmentation
        test_preprocessor = WellLogPreprocessor(normalization='standard')
        test_preprocessor.fit(features, targets, feature_names)

        augmented_features, updated_feature_names = test_preprocessor.augment_with_pseudolabels(
            test_preprocessor.transform_features(features), physics_coupling, feature_names
        )
        print(f"   ✅ Augmented features shape: {augmented_features.shape}")
        print(f"   ✅ Updated feature names: {updated_feature_names}")
        
        # Test Step 3: Multiparameter Physics Model Support
        print("\n3️⃣ Testing Step 3: Multiparameter Model Support")
        
        multiparameter_model = RockPhysicsFactory.create('multiparameter')
        multiparameter_coupling = PhysicsCouplingManager(config, 0, multiparameter_model)
        
        # Test multiparameter detection
        is_multiparameter = hasattr(multiparameter_model, 'feature_names') and len(getattr(multiparameter_model, 'feature_names', [])) > 1
        print(f"   ✅ Multiparameter detection: {is_multiparameter}")
        
        # Test feature mapping
        feature_mapping = multiparameter_coupling._check_feature_mapping(
            feature_names, ['GR', 'DEN', 'VP', 'RES']
        )
        print(f"   ✅ Feature mapping: {feature_mapping}")
        
        # Test multiparameter predictions
        multiparameter_pred = multiparameter_coupling.compute_physics_predictions(features)
        print(f"   ✅ Multiparameter predictions shape: {multiparameter_pred.shape}")
        
        # Test Integration: All components working together
        print("\n🔗 Testing Integration: All Components Together")
        
        # Create trainer with physics coupling
        strategy = PhysicsGuidanceStrategy('pseudolabels')
        strategy_handler = StrategyHandler(strategy, rock_physics_model)
        model = BiGRU(**config['model']['params'])
        
        trainer = PhysicsGuidedTrainer(
            model=model,
            rock_physics_model=rock_physics_model,
            strategy_handler=strategy_handler,
            config=config,
            preprocessor=preprocessor
        )
        
        print(f"   ✅ Trainer has physics coupling: {trainer.physics_coupling is not None}")
        print(f"   ✅ Strategy handler has physics coupling: {strategy_handler.physics_coupling is not None}")
        
        # Test that the enhanced pipeline will work
        print("\n🧪 Testing Enhanced Pipeline Compatibility")
        
        from test_las_transfer_learning_pipeline import LASTransferLearningPipelineTest
        
        # Create pipeline instance
        pipeline = LASTransferLearningPipelineTest(enable_transfer_learning=False)
        
        # Check test configurations
        test_configs = pipeline.config.get('test_configurations', {})
        print(f"   ✅ Test configurations available: {list(test_configs.keys())}")
        
        # Verify each test configuration has the right structure
        for name, test_config in test_configs.items():
            model_type = test_config.get('rock_physics', {}).get('model_type')
            strategy = test_config.get('training', {}).get('strategy')
            print(f"   ✅ {name}: {model_type} + {strategy}")
        
        print("\n" + "="*80)
        print("🎉 ALL PHYSICS INTEGRATION IMPROVEMENTS SUCCESSFULLY TESTED!")
        print("="*80)
        
        print("\n📋 Summary of Improvements:")
        print("✅ Step 1: Centralized Physics Unit Coupling - Working")
        print("✅ Step 2: Sequence-Aware Pseudolabel Augmentation - Working") 
        print("✅ Step 3: Multiparameter Physics Teacher Enablement - Working")
        print("✅ Enhanced Pipeline Integration - Ready")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_physics_integration_improvements()
    if success:
        print("\n🚀 Enhanced physics integration is ready for use!")
        print("   Run: python test_las_transfer_learning_pipeline.py")
        print("   The pipeline will automatically test all three improvements.")
    else:
        print("\n⚠️  Some issues detected. Please check the error messages above.")
