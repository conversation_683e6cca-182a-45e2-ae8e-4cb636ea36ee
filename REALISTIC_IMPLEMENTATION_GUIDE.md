# Realistic Implementation Guide: Bridging Codebase to <PERSON> et al. (2024)

## Overview


## Progress Update (2025-09-24)

- Implemented:
  - ✅ Unit- and index-consistent physics coupling in trainer
    - Config-driven VP feature selection via data.vp_feature_name
    - Proper denormalization and unit conversion (m/s → km/s) using physics_coupling.conversion_factor=0.001
    - Physics predictions are re-normalized to the target scale when scalers are present
  - ✅ Two-stage transfer learning (pretrain on physics targets → fine-tune on true targets)
    - Separate optimizers/LRs per stage (e.g., 0.001 → 0.0001)
    - Early stopping for fine-tuning
    - Logging improved to avoid misleading cross-stage “Improvement” and instead report Stage‑2 (fine-tuning) improvement
  - ✅ Rock-physics models implemented and factory-registered: mudrock_line, empirical_vpvs, multiparameter

- Notes:
  - The trainer’s compute_physics_predictions currently implements the VP-only path; using the multiparameter model in pretraining requires extending this method to pass GR/DEN/VP/RES (see Optional Next Improvements).
  - WellLogPreprocessor defaults to feature_range=(0,1). To match the paper, set [-1,1] via normalization_params.
  - Configuration convention: physics_coupling.conversion_factor=0.001 means m/s → km/s. Keep this consistent across the codebase.

Configuration example (align with current implementation):
```yaml
physics_coupling:
  vp_feature_name: "P-WAVE"     # Must exist in data.features
  conversion_factor: 0.001      # m/s to km/s
preprocessing:
  normalization_params:
    feature_range: [-1, 1]
  log_transform_features: ["RES"]
```

## Optional Next Improvements (2025-09-24)

- Multiparameter physics coupling in trainer
  - Extend compute_physics_predictions to denormalize GR, DEN, VP, RES to physical units; convert VP to km/s; call rock_physics_model.predict with a dict; convert VS back to m/s; normalize to target scale if scalers exist.
- Pseudolabel channel integration (sequence-compatible)
  - During preprocessing, compute physics VS from denormalized VP and append as an extra channel before normalization; update model input_dim accordingly; ensure consistent transforms across splits.
- Normalization and log transform alignment
  - Enforce feature_range [-1,1] and apply log10(RES) at preprocessing (not only inside specific physics models).
- Baseline-from-scratch path and comparison logs
  - Add a training-from-scratch run on the same data/splits; report deltas vs transfer learning (RMSE, R², correlation) on val/test.
- Stage‑2 RMSE improvement log
  - In addition to loss delta, report RMSE delta from fine‑tuning epoch‑1 to best epoch.
- Blind test protocol (paper fidelity)
  - Orchestrate train-one-well/test-four-wells with per-well and averaged metrics (mean ± std), plus crossplots and error analysis.
- Reproducibility
  - Deterministic seeding and artifact persistence (configs, scalers, models), plus experiment metadata for replay.

This guide provides a **simple, practical approach** to address the critical gaps between the current codebase and the reference paper (Zhao et al., 2024). Based on the Implementation Analysis, we focus on the highest-impact, lowest-complexity improvements that will bring the alignment from 84% to ~95%.

## Current State Summary (Updated: 2025-09-24)

**What’s Working Well:**
- ✅ BiGRU architecture (100% aligned)
- ✅ Physics-guided loss function (Equation 10)
- ✅ Training infrastructure
- ✅ Unit- and index-consistent physics coupling in trainer (config-driven VP channel; conversion_factor=0.001 m/s→km/s)
- ✅ Two-stage transfer learning (pretrain on physics targets → fine-tune on true targets), with improved logging
- ✅ Rock-physics models implemented and factory-registered: mudrock_line, empirical_vpvs; multiparameter model available

**Remaining Gaps:**
- 🟡 Multiparameter physics coupling not yet wired into trainer’s compute_physics_predictions (current path is VP-only)
- 🟡 Pseudolabel channel not integrated for sequences
- 🟡 Normalization policy (-1,1) and log10(RES) not enforced in the LAS pipeline
- ⚪ Blind test protocol (train one well, test four wells) not implemented

## Phase 1: Critical Fixes (1 week, +3% alignment)

### 1.1 Fix Unit Consistency (2-3 days)

**Problem:** Physics models expect VP in km/s but receive normalized [-1,1] values.

**Simple Solution:**

1. **Add unit conversion config** in `configs/default_config.yaml`:
```yaml
physics_coupling:
  vp_feature_name: "VP"  # Name of VP in features list
  conversion_factor: 1000.0  # m/s to km/s
  expected_vp_range: [1500, 6000]  # m/s for validation
```

2. **Update trainer** to handle units properly:
```python
# In src/training/trainer.py, modify compute_physics_predictions()
def compute_physics_predictions(self, features, preprocessor=None):
    # Get VP index from config
    vp_name = self.config.get('physics_coupling', {}).get('vp_feature_name', 'VP')
    vp_idx = self.config['data']['features'].index(vp_name)
    
    # Extract VP and denormalize if needed
    vp_normalized = features[:, :, vp_idx]  # [B, T]
    
    if preprocessor is not None:
        # Denormalize to physical units (m/s)
        vp_ms = preprocessor.inverse_transform_single_feature(vp_normalized, vp_idx)
        # Convert to km/s for physics models
        vp_kms = vp_ms / self.config.get('physics_coupling', {}).get('conversion_factor', 1000.0)
    else:
        # Assume already in correct units
        vp_kms = vp_normalized
    
    # Apply physics model
    vs_kms = self.rock_physics_model.predict(vp_kms)
    
    # Convert back to m/s to match target units
    vs_ms = vs_kms * self.config.get('physics_coupling', {}).get('conversion_factor', 1000.0)
    
    return vs_ms
```

3. **Add inverse transform method** to preprocessor:
```python
# In src/data/preprocessing.py
def inverse_transform_single_feature(self, normalized_data, feature_idx):
    """Denormalize a single feature back to original scale"""
    if self.scaler is None:
        return normalized_data
    
    # Create a dummy array with all features
    dummy = np.zeros((normalized_data.shape[0], len(self.feature_names)))
    dummy[:, feature_idx] = normalized_data.flatten()
    
    # Inverse transform
    denormalized = self.scaler.inverse_transform(dummy)
    
    # Extract just the feature we want
    return denormalized[:, feature_idx].reshape(normalized_data.shape)
```

4. **Add validation test**:
```python
# In tests/test_unit_consistency.py
def test_physics_units():
    # Create synthetic data in physical units
    vp_ms = np.array([3000, 4000, 5000])  # m/s
    expected_vs_kms = np.array([1.73, 2.31, 2.89])  # km/s from mudrock line
    
    # Test physics model
    model = MudrockLine()
    vs_kms = model.predict(vp_ms / 1000.0)  # Convert to km/s
    
    np.testing.assert_allclose(vs_kms, expected_vs_kms, rtol=0.01)
```

### 1.2 Add RES Log Transform (1 day)

**Simple Solution:**

1. **Update preprocessing config**:
```yaml
preprocessing:
  log_transform_features: ["RES"]  # Apply log10 to these features
  normalization_range: [-1, 1]  # Ensure correct range
```

2. **Modify preprocessor**:
```python
# In src/data/preprocessing.py
def fit(self, data):
    # Apply log transform to specified features BEFORE normalization
    log_features = self.config.get('preprocessing', {}).get('log_transform_features', [])
    self.log_indices = []
    
    for feature in log_features:
        if feature in self.feature_names:
            idx = self.feature_names.index(feature)
            self.log_indices.append(idx)
            # Add small epsilon to avoid log(0)
            data[:, idx] = np.log10(data[:, idx] + 1e-8)
    
    # Now fit the scaler on log-transformed data
    self.scaler = MinMaxScaler(feature_range=self.config.get('preprocessing', {}).get('normalization_range', [-1, 1]))
    self.scaler.fit(data)
    
def transform(self, data):
    # Apply log transform to same features
    data_copy = data.copy()
    for idx in self.log_indices:
        data_copy[:, idx] = np.log10(data_copy[:, idx] + 1e-8)
    
    # Then normalize
    return self.scaler.transform(data_copy)
```

## Phase 2: Core Methodology (2-3 weeks, +12% alignment)

### 2.1 Add Missing Rock Physics Constraints (1 week)

**Keep it simple - just implement the equations:**

1. **Create Empirical VP-VS** (Equation 6):
```python
# Create new file: src/models/rock_physics/empirical_vpvs.py
import numpy as np
from .base import RockPhysicsModel

class EmpiricalVPVS(RockPhysicsModel):
    """Empirical VP-VS relationship: VS = a * VP^b
    
    Based on Castagna et al. (1985) mudrock line generalization.
    Default parameters from Zhao et al. (2024).
    """
    
    def __init__(self, a=0.8619, b=0.8621):
        """
        Args:
            a: Multiplicative coefficient (default from paper)
            b: Power coefficient (default from paper)
        """
        self.a = a
        self.b = b
        self.name = "empirical_vpvs"
    
    def predict(self, vp):
        """
        Predict VS from VP using power law relationship.
        
        Args:
            vp: P-wave velocity in km/s
            
        Returns:
            vs: S-wave velocity in km/s
        """
        return self.a * np.power(vp, self.b)
    
    def fit(self, vp_data, vs_data):
        """Fit coefficients to well-specific data"""
        # Log transform for linear regression
        log_vp = np.log(vp_data)
        log_vs = np.log(vs_data)
        
        # Linear regression in log space
        A = np.vstack([log_vp, np.ones(len(log_vp))]).T
        self.b, log_a = np.linalg.lstsq(A, log_vs, rcond=None)[0]
        self.a = np.exp(log_a)
        
        return self
```

2. **Create Multiparameter Regression** (Equation 7):
```python
# Create new file: src/models/rock_physics/multiparameter.py
import numpy as np
from .base import RockPhysicsModel

class MultiparameterRegression(RockPhysicsModel):
    """Multiparameter regression for VS prediction.
    
    VS = a*GR + b*DEN + c*VP + d*RES + e
    
    Based on Zhao et al. (2024) Equation 7.
    """
    
    def __init__(self, coefficients=None):
        """
        Args:
            coefficients: Dict with keys 'GR', 'DEN', 'VP', 'RES', 'intercept'
        """
        # Default coefficients (these should be fitted to data)
        self.coefficients = coefficients or {
            'GR': 0.002,      # Gamma ray coefficient
            'DEN': 0.5,       # Density coefficient  
            'VP': 0.3,        # P-wave coefficient
            'RES': -0.001,    # Resistivity coefficient (log-transformed)
            'intercept': 1.0  # Intercept term
        }
        self.name = "multiparameter"
        self.feature_names = ['GR', 'DEN', 'VP', 'RES']
    
    def predict(self, features_dict):
        """
        Predict VS from multiple well logs.
        
        Args:
            features_dict: Dict with keys matching feature_names, values in appropriate units:
                - GR: API units
                - DEN: g/cm³
                - VP: km/s
                - RES: ohm.m (will be log-transformed internally)
                
        Returns:
            vs: S-wave velocity in km/s
        """
        vs = self.coefficients['intercept']
        
        for feature in self.feature_names:
            if feature in features_dict:
                value = features_dict[feature]
                # Log transform resistivity
                if feature == 'RES':
                    value = np.log10(value + 1e-8)
                vs += self.coefficients[feature] * value
                
        return vs
    
    def fit(self, features_dict, vs_target):
        """Fit coefficients using linear regression"""
        # Build feature matrix
        X = []
        for feature in self.feature_names:
            if feature in features_dict:
                values = features_dict[feature]
                if feature == 'RES':
                    values = np.log10(values + 1e-8)
                X.append(values)
        
        X = np.column_stack(X)
        # Add intercept column
        X = np.column_stack([X, np.ones(len(vs_target))])
        
        # Solve linear regression
        coeffs = np.linalg.lstsq(X, vs_target, rcond=None)[0]
        
        # Update coefficients
        for i, feature in enumerate(self.feature_names):
            self.coefficients[feature] = coeffs[i]
        self.coefficients['intercept'] = coeffs[-1]
        
        return self
```

3. **Register in factory**:
```python
# In src/models/rock_physics/__init__.py
from .empirical_vpvs import EmpiricalVPVS
from .multiparameter import MultiparameterRegression

# Add to the factory
ROCK_PHYSICS_MODELS['empirical_vpvs'] = EmpiricalVPVS
ROCK_PHYSICS_MODELS['multiparameter'] = MultiparameterRegression
```

4. **Update trainer to support multiparameter model**:
```python
# In src/training/trainer.py
def compute_physics_predictions(self, features, preprocessor=None):
    """Compute physics predictions based on model type"""
    
    if self.rock_physics_model.name == 'multiparameter':
        # Extract all required features
        features_dict = {}
        for i, feature_name in enumerate(self.config['data']['features']):
            if feature_name in ['VP', 'GR', 'DEN', 'RES']:
                feature_data = features[:, :, i]  # [B, T]
                
                if preprocessor is not None:
                    # Denormalize to physical units
                    feature_data = preprocessor.inverse_transform_single_feature(feature_data, i)
                
                # Convert VP to km/s if needed
                if feature_name == 'VP':
                    feature_data = feature_data / 1000.0  # m/s to km/s
                    
                features_dict[feature_name] = feature_data.flatten()
        
        # Predict VS
        vs_kms = self.rock_physics_model.predict(features_dict)
        vs_ms = vs_kms * 1000.0  # Convert back to m/s
        
        # Reshape to match input
        return vs_ms.reshape(features.shape[0], features.shape[1])
    
    else:
        # Original VP-only models (mudrock, empirical)
        # ... existing code ...
```

### 2.2 Minimal Transfer Learning (1 week)

**Simplest approach - two-stage training in existing trainer:**

1. **Add config**:
```yaml
transfer_learning:
  enabled: false  # Off by default
  pretrain_epochs: 50
  pretrain_lr: 0.001
  finetune_epochs: 100
  finetune_lr: 0.0001
  early_stopping_patience: 10
  save_pretrained: true
  pretrained_path: "output/models/pretrained_bigru.pth"
```

2. **Create transfer learning helper**:
```python
# In src/training/transfer_learning.py
import torch
import numpy as np
from typing import Dict, Tuple

class TransferLearningHelper:
    """Helper class for two-stage transfer learning"""
    
    def __init__(self, config: dict, trainer, model, loss_fn):
        self.config = config['transfer_learning']
        self.trainer = trainer
        self.model = model
        self.loss_fn = loss_fn
        
    def pretrain_stage(self, train_loader, val_loader, preprocessor=None):
        """Stage 1: Pretrain on physics-derived targets"""
        print("\n=== Transfer Learning Stage 1: Pretraining on Physics Targets ===")
        
        # Create optimizer for pretraining
        optimizer = torch.optim.Adam(
            self.model.parameters(), 
            lr=self.config['pretrain_lr']
        )
        
        best_val_loss = float('inf')
        
        for epoch in range(self.config['pretrain_epochs']):
            # Training with physics targets
            epoch_loss = 0.0
            num_batches = 0
            
            self.model.train()
            for batch_features, batch_targets in train_loader:
                # Compute physics targets for this batch
                with torch.no_grad():
                    physics_targets = self.trainer.compute_physics_predictions(
                        batch_features, preprocessor
                    )
                    physics_targets = torch.tensor(
                        physics_targets, 
                        dtype=torch.float32,
                        device=batch_features.device
                    )
                
                # Forward pass
                optimizer.zero_grad()
                predictions = self.model(batch_features)
                
                # Use physics targets instead of true targets
                loss = self.loss_fn(predictions, physics_targets)
                
                # Backward pass
                loss.backward()
                optimizer.step()
                
                epoch_loss += loss.item()
                num_batches += 1
            
            # Validation
            val_metrics = self._validate_physics(val_loader, preprocessor)
            
            print(f"Pretrain Epoch {epoch+1}/{self.config['pretrain_epochs']}: "
                  f"Loss = {epoch_loss/num_batches:.4f}, "
                  f"Val RMSE = {val_metrics['rmse']:.4f}")
            
            # Save best model
            if val_metrics['loss'] < best_val_loss:
                best_val_loss = val_metrics['loss']
                if self.config['save_pretrained']:
                    torch.save(
                        self.model.state_dict(), 
                        self.config['pretrained_path']
                    )
                    print(f"  Saved pretrained model (val_loss: {best_val_loss:.4f})")
        
        return best_val_loss
    
    def finetune_stage(self, train_loader, val_loader):
        """Stage 2: Finetune on true targets"""
        print("\n=== Transfer Learning Stage 2: Finetuning on True Targets ===")
        
        # Load pretrained weights if saved
        if self.config['save_pretrained']:
            try:
                self.model.load_state_dict(
                    torch.load(self.config['pretrained_path'], map_location='cpu')
                )
                print("Loaded pretrained weights")
            except:
                print("Warning: Could not load pretrained weights, continuing with current weights")
        
        # Create optimizer with reduced learning rate
        optimizer = torch.optim.Adam(
            self.model.parameters(), 
            lr=self.config['finetune_lr']
        )
        
        best_val_loss = float('inf')
        patience_counter = 0
        
        for epoch in range(self.config['finetune_epochs']):
            # Standard training on true targets
            train_loss = self.trainer.train_epoch(train_loader, optimizer, self.loss_fn)
            val_metrics = self.trainer.evaluate(val_loader, self.loss_fn)
            
            print(f"Finetune Epoch {epoch+1}/{self.config['finetune_epochs']}: "
                  f"Train Loss = {train_loss:.4f}, "
                  f"Val RMSE = {val_metrics['rmse']:.4f}")
            
            # Early stopping
            if val_metrics['loss'] < best_val_loss:
                best_val_loss = val_metrics['loss']
                patience_counter = 0
                torch.save(self.model.state_dict(), 'output/models/best_model.pth')
            else:
                patience_counter += 1
                if patience_counter >= self.config['early_stopping_patience']:
                    print(f"Early stopping triggered at epoch {epoch+1}")
                    break
        
        # Load best model
        self.model.load_state_dict(
            torch.load('output/models/best_model.pth', map_location='cpu')
        )
        
        return best_val_loss
    
    def _validate_physics(self, val_loader, preprocessor):
        """Validate using physics targets"""
        self.model.eval()
        total_loss = 0.0
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch_features, batch_targets in val_loader:
                # Get physics targets
                physics_targets = self.trainer.compute_physics_predictions(
                    batch_features, preprocessor
                )
                physics_targets = torch.tensor(
                    physics_targets, 
                    dtype=torch.float32,
                    device=batch_features.device
                )
                
                # Predict
                predictions = self.model(batch_features)
                loss = self.loss_fn(predictions, physics_targets)
                
                total_loss += loss.item()
                all_predictions.extend(predictions.cpu().numpy().flatten())
                all_targets.extend(physics_targets.cpu().numpy().flatten())
        
        # Compute metrics
        all_predictions = np.array(all_predictions)
        all_targets = np.array(all_targets)
        
        rmse = np.sqrt(np.mean((all_predictions - all_targets) ** 2))
        
        return {
            'loss': total_loss / len(val_loader),
            'rmse': rmse
        }
```

3. **Modify training script** (examples/train_model.py):
```python
# After model/trainer setup
if config.get('transfer_learning', {}).get('enabled', False):
    from src.training.transfer_learning import TransferLearningHelper
    
    # Create transfer learning helper
    tl_helper = TransferLearningHelper(config, trainer, model, loss_fn)
    
    # Run two-stage training
    pretrain_loss = tl_helper.pretrain_stage(train_loader, val_loader, preprocessor)
    finetune_loss = tl_helper.finetune_stage(train_loader, val_loader)
    
    print(f"\nTransfer Learning Complete!")
    print(f"Best Pretrain Loss: {pretrain_loss:.4f}")
    print(f"Best Finetune Loss: {finetune_loss:.4f}")
    
else:
    # Original single-stage training
    print("\n=== Standard Training (No Transfer Learning) ===")
    # ... existing training code ...
```

## Phase 3: Testing and Validation (3-5 days)

### 3.1 Comprehensive Unit Tests

Create `tests/test_physics_improvements.py`:

```python
import pytest
import numpy as np
import torch
from src.models.rock_physics import create_rock_physics_model
from src.data.preprocessing import WellLogPreprocessor
from src.training.trainer import PhysicsGuidedTrainer

class TestPhysicsImprovements:
    
    def test_unit_conversions(self):
        """Test that unit conversions work correctly"""
        # Test data in physical units
        vp_ms = np.array([3000, 4000, 5000])  # m/s
        vp_kms = vp_ms / 1000.0  # km/s
        
        # Test each physics model
        models = ['mudrock_line', 'empirical_vpvs']
        
        for model_name in models:
            model = create_rock_physics_model(model_name)
            vs_kms = model.predict(vp_kms)
            
            # Check output is in reasonable range
            assert np.all(vs_kms > 0.5)  # VS should be > 0.5 km/s
            assert np.all(vs_kms < vp_kms)  # VS should be < VP
    
    def test_multiparameter_model(self):
        """Test multiparameter regression model"""
        model = create_rock_physics_model('multiparameter')
        
        # Test data
        features = {
            'VP': np.array([3.0, 4.0, 5.0]),  # km/s
            'GR': np.array([30, 60, 90]),     # API
            'DEN': np.array([2.2, 2.4, 2.6]), # g/cm³
            'RES': np.array([10, 100, 1000])  # ohm.m
        }
        
        vs = model.predict(features)
        
        # Basic sanity checks
        assert vs.shape == (3,)
        assert np.all(vs > 0)
        assert np.all(vs < features['VP'])  # VS < VP
    
    def test_preprocessor_inverse_transform(self):
        """Test that inverse transform recovers original units"""
        config = {
            'preprocessing': {
                'normalization_range': [-1, 1],
                'log_transform_features': ['RES']
            }
        }
        
        # Create test data
        data = np.array([
            [3000, 50, 2.3, 100],  # VP, GR, DEN, RES
            [4000, 70, 2.5, 200],
            [5000, 90, 2.7, 300]
        ])
        
        feature_names = ['VP', 'GR', 'DEN', 'RES']
        
        # Fit preprocessor
        preprocessor = WellLogPreprocessor(config, feature_names)
        preprocessor.fit(data)
        
        # Transform and inverse transform
        normalized = preprocessor.transform(data)
        recovered = np.zeros_like(data)
        
        for i in range(len(feature_names)):
            recovered[:, i] = preprocessor.inverse_transform_single_feature(
                normalized[:, i], i
            )
        
        # Check recovery (allowing for small numerical errors)
        np.testing.assert_allclose(recovered, data, rtol=1e-5)
    
    def test_transfer_learning_integration(self):
        """Test that transfer learning runs without errors"""
        # This is more of an integration test
        config = {
            'transfer_learning': {
                'enabled': True,
                'pretrain_epochs': 2,  # Small for testing
                'finetune_epochs': 2,
                'pretrain_lr': 0.001,
                'finetune_lr': 0.0001,
                'early_stopping_patience': 5,
                'save_pretrained': False
            },
            'data': {
                'features': ['VP', 'GR', 'DEN', 'RES']
            },
            'physics_coupling': {
                'vp_feature_name': 'VP',
                'conversion_factor': 1000.0
            }
        }
        
        # Create dummy data
        n_samples = 100
        n_features = 4
        sequence_length = 10
        
        features = torch.randn(n_samples, sequence_length, n_features)
        targets = torch.randn(n_samples, sequence_length)
        
        # This should run without errors
        # (actual training would happen in the full pipeline)
        assert config['transfer_learning']['enabled'] == True
```

### 3.2 A/B Testing Script

Create `scripts/ab_test_improvements.py`:

```python
"""A/B test to compare performance with and without improvements"""

import json
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt

def run_ab_test():
    """Run models with and without improvements"""
    
    results = {
        'baseline': {},
        'with_improvements': {}
    }
    
    # Test 1: Baseline (no improvements)
    print("Running baseline model...")
    config_baseline = {
        'transfer_learning': {'enabled': False},
        'physics_coupling': {'vp_feature_name': 'VP'},  # But no unit conversion
        'preprocessing': {}  # No log transform
    }
    
    # Run baseline (pseudo-code - replace with actual pipeline)
    # baseline_metrics = run_pipeline(config_baseline)
    # results['baseline'] = baseline_metrics
    
    # Test 2: With improvements
    print("Running improved model...")
    config_improved = {
        'transfer_learning': {'enabled': True},
        'physics_coupling': {
            'vp_feature_name': 'VP',
            'conversion_factor': 1000.0
        },
        'preprocessing': {
            'log_transform_features': ['RES']
        }
    }
    
    # Run improved (pseudo-code - replace with actual pipeline)
    # improved_metrics = run_pipeline(config_improved)
    # results['with_improvements'] = improved_metrics
    
    # Save results
    with open('output/ab_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    # Create comparison plot
    create_comparison_plot(results)
    
    return results

def create_comparison_plot(results):
    """Create bar chart comparing metrics"""
    
    metrics = ['rmse', 'r2', 'correlation']
    baseline_values = [results['baseline'].get(m, 0) for m in metrics]
    improved_values = [results['with_improvements'].get(m, 0) for m in metrics]
    
    x = np.arange(len(metrics))
    width = 0.35
    
    fig, ax = plt.subplots(figsize=(10, 6))
    ax.bar(x - width/2, baseline_values, width, label='Baseline')
    ax.bar(x + width/2, improved_values, width, label='With Improvements')
    
    ax.set_xlabel('Metrics')
    ax.set_ylabel('Values')
    ax.set_title('Model Performance Comparison')
    ax.set_xticks(x)
    ax.set_xticklabels(metrics)
    ax.legend()
    
    plt.tight_layout()
    plt.savefig('output/ab_test_comparison.png')
    plt.close()

if __name__ == "__main__":
    results = run_ab_test()
    print("\nA/B Test Complete!")
    print(f"Results saved to output/ab_test_results.json")
```

## Implementation Checklist

### Week 1: Critical Fixes
- [ ] Fix unit consistency in physics coupling
  - [ ] Add physics_coupling config section
  - [ ] Implement inverse_transform_single_feature
  - [ ] Update compute_physics_predictions
  - [ ] Add unit tests for conversions
- [ ] Add RES log transform
  - [ ] Update preprocessing config
  - [ ] Modify fit/transform methods
  - [ ] Test with synthetic data
- [ ] Run full pipeline test with fixes
- [ ] Document changes in CHANGES_SUMMARY.md

### Week 2-3: Core Methodology
- [ ] Implement empirical VP-VS model
  - [ ] Create empirical_vpvs.py
  - [ ] Add fit method for well-specific calibration
  - [ ] Register in factory
- [ ] Implement multiparameter regression
  - [ ] Create multiparameter.py
  - [ ] Handle multiple input features
  - [ ] Update trainer for multi-feature support
- [ ] Add minimal transfer learning
  - [ ] Create TransferLearningHelper class
  - [ ] Implement two-stage training
  - [ ] Add config options
  - [ ] Test with small dataset
- [ ] Run A/B tests comparing with/without improvements

### Week 4: Polish and Validate
- [ ] Run full pipeline tests on all LAS files
- [ ] Create performance comparison plots
- [ ] Update documentation
  - [ ] Add config examples
  - [ ] Document new rock physics models
  - [ ] Create usage guide for transfer learning
- [ ] Create example notebooks
  - [ ] Unit conversion demonstration
  - [ ] Rock physics model comparison
  - [ ] Transfer learning walkthrough

## Key Principles

1. **Keep it Simple**: Don't over-engineer. The paper's methods are straightforward.
2. **Maintain Compatibility**: All changes should be backward-compatible with config flags.
3. **Test Incrementally**: Verify each change works before moving to the next.
4. **Focus on Impact**: Prioritize changes that directly improve model performance.

## Expected Outcomes

After implementing these changes:
- **Alignment Score**: 84% → ~96%
- **Model Performance**: 
  - RMSE reduction: ~10-15% (from proper unit handling)
  - Better generalization: ~20% improvement on blind wells (from transfer learning)
  - More stable training: Reduced variance across runs
- **Physics Consistency**: VS predictions within physical bounds
- **Robustness**: Better performance on wells with different characteristics

## Common Pitfalls to Avoid

1. **Don't break existing functionality** - Use config flags for new features
2. **Don't assume units** - Always validate and document unit conversions
3. **Don't skip tests** - Each physics model needs unit tests
4. **Don't complicate** - The paper's approach is simple; keep it that way
5. **Don't mix units** - Be consistent: m/s for data, km/s for physics models

## Quick Start

```bash
# 1. Create a new branch
git checkout -b implement-physics-improvements

# 2. Start with unit consistency fix
# Edit src/training/trainer.py as shown above

# 3. Run tests to ensure nothing breaks
pytest tests/

# 4. Test with physics guidance
python examples/train_model.py --config configs/default_config.yaml

# 5. Enable transfer learning
# Set transfer_learning.enabled: true in config
python examples/train_model.py --config configs/default_config.yaml

# 6. Run A/B test
python scripts/ab_test_improvements.py
```

## Validation Metrics

Track these metrics before/after changes:

| Metric | Baseline | Expected Improvement |
|--------|----------|---------------------|
| RMSE (m/s) | ~150-200 | ~130-170 (-15%) |
| R² | ~0.85 | ~0.90 (+6%) |
| Physics Consistency | 60% | 95% (+58%) |
| Cross-well RMSE | ~250 | ~200 (-20%) |

**Physics Consistency** = Percentage of predictions where |VS_pred - VS_physics| / VS_physics < 0.1

## Next Steps

1. **Immediate** (This Week):
   - Implement Phase 1 fixes (unit consistency + RES transform)
   - Run tests to verify no regression
   - Document changes

2. **Short Term** (Next 2 Weeks):
   - Add missing rock physics models
   - Implement transfer learning
   - Run A/B tests

3. **Medium Term** (Month 2):
   - Fine-tune on real well data
   - Optimize hyperparameters
   - Create production deployment guide

4. **Long Term** (Month 3+):
   - Extend to other rock physics models
   - Add uncertainty quantification
   - Implement ensemble methods

## Support and Troubleshooting

### Common Issues and Solutions

1. **Physics predictions are unrealistic**
   - Check unit conversions (m/s vs km/s)
   - Verify feature indices match config
   - Ensure preprocessor is passed correctly

2. **Transfer learning doesn't improve results**
   - Try different learning rates (pretrain_lr)
   - Increase pretrain_epochs
   - Check physics model predictions are reasonable

3. **Memory issues with multiparameter model**
   - Process in smaller batches
   - Use gradient accumulation
   - Reduce sequence length if needed

4. **Tests failing after changes**
   - Run `pytest -v` for detailed output
   - Check config structure matches expected format
   - Verify all imports are correct

### Getting Help

- Check existing tests for usage examples
- Review paper for theoretical background
- Create minimal reproducible examples for debugging

## Conclusion

This guide provides a practical, incremental approach to improving the codebase alignment with Zhao et al. (2024). By focusing on high-impact, low-complexity changes first, you can achieve significant improvements (~12% alignment increase) with minimal risk to existing functionality.

Remember: The goal is not perfection but practical improvement. Start with Phase 1, validate the improvements work, then proceed to Phase 2. Each phase builds on the previous one, ensuring stable progress toward full paper alignment.