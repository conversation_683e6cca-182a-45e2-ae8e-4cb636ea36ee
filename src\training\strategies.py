from enum import Enum
from typing import Dict, Any, Optional
import torch
import numpy as np

class PhysicsGuidanceStrategy(Enum):
    """Enumeration of physics guidance strategies."""
    PSEUDOLABELS = "pseudolabels"
    LOSS_FUNCTION = "loss_function"
    TRANSFER_LEARNING = "transfer_learning"

class StrategyHandler:
    """Handler for different physics guidance strategies."""

    def __init__(self, strategy: PhysicsGuidanceStrategy, rock_physics_model, physics_coupling=None):
        self.strategy = strategy
        self.rock_physics_model = rock_physics_model
        self.physics_coupling = physics_coupling

    def prepare_features(
        self,
        features: np.ndarray,
        vp_index: Optional[int] = None,
        preprocessor: Optional[Any] = None
    ) -> np.ndarray:
        """
        Prepare features based on strategy.

        Args:
            features: Input features array
            vp_index: Index of VP in features
            preprocessor: Data preprocessor for unit conversion (optional)

        Returns:
            Modified features array
        """
        if self.strategy == PhysicsGuidanceStrategy.PSEUDOLABELS:
            if self.physics_coupling is not None:
                # Use centralized physics coupling manager for unit-aware predictions
                vs_physics = self.physics_coupling.compute_physics_predictions(
                    features, preprocessor
                )
                # Handle different dimensionalities
                if vs_physics.ndim == 3 and features.ndim == 2:
                    # If physics predictions are 3D but features are 2D, flatten
                    vs_physics = vs_physics.reshape(-1)
                elif vs_physics.ndim == 2 and features.ndim == 2:
                    # If both are 2D, use as is
                    pass

                # Add physics predictions as additional feature
                if features.ndim == 2:  # [N, F]
                    if vs_physics.ndim == 1:
                        features = np.column_stack([features, vs_physics])
                    else:  # vs_physics is 2D
                        features = np.column_stack([features, vs_physics.flatten()])
                else:  # 3D features [B, T, F]
                    if vs_physics.ndim == 2:  # [B, T]
                        vs_physics = vs_physics[..., np.newaxis]  # [B, T, 1]
                    features = np.concatenate([features, vs_physics], axis=-1)
            else:
                # Fallback to original logic (without unit conversion)
                if vp_index is None:
                    raise ValueError("vp_index must be provided to prepare_features when physics_coupling is not available.")
                # Extract VP and predict VS using physics model directly
                if features.ndim == 2:  # [N, F]
                    vp = features[:, vp_index]
                else:  # 3D features [B, T, F]
                    vp = features[:, :, vp_index]

                vs_physics = self.rock_physics_model.predict(vp)

                # Add physics predictions as additional feature
                if features.ndim == 2:
                    features = np.column_stack([features, vs_physics])
                else:  # 3D
                    if vs_physics.ndim == 2:  # [B, T]
                        vs_physics = vs_physics[..., np.newaxis]  # [B, T, 1]
                    features = np.concatenate([features, vs_physics], axis=-1)

        return features

    def get_loss_function(self):
        """Get appropriate loss function for strategy."""
        if self.strategy == PhysicsGuidanceStrategy.LOSS_FUNCTION:
            from .losses import PhysicsGuidedLoss
            return PhysicsGuidedLoss()
        else:
            return torch.nn.MSELoss()
