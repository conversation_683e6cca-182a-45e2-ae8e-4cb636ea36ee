#!/usr/bin/env python3
"""
Simple test to verify the unit conversion fix is working correctly.
"""
import sys
import os
import yaml
import torch
import numpy as np

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from models.neural_networks import BiGRU
from models.rock_physics import RockPhysicsFactory
from training.strategies import PhysicsGuidanceStrategy, StrategyHandler
from training.trainer import PhysicsGuidedTrainer
from data.preprocessing import WellLogPreprocessor

def test_unit_conversion():
    """Test that unit conversion is working correctly."""
    print("Testing Unit Conversion Fix")
    print("=" * 50)
    
    # Load config
    with open("configs/default_config.yaml", "r") as f:
        config = yaml.safe_load(f)
    
    # Create simple test data
    n_samples, seq_len = 10, 20
    n_features = len(config['data']['features'])
    
    # Realistic P-wave data (m/s)
    p_wave = np.random.uniform(2000, 6000, (n_samples, seq_len))
    rhob = np.random.uniform(1.8, 2.8, (n_samples, seq_len))
    phie = np.random.uniform(0.05, 0.35, (n_samples, seq_len))
    rt = np.random.uniform(0.1, 1000, (n_samples, seq_len))
    
    # Ground truth S-wave using mudrock line
    s_wave = ((p_wave / 1000.0) - 1.36) / 1.16 * 1000.0  # Convert to m/s
    s_wave = np.clip(s_wave, 500, 4000)
    
    features = np.stack([p_wave, rhob, phie, rt], axis=-1)
    targets = s_wave[..., np.newaxis]
    
    print(f"Original data ranges:")
    print(f"  P-wave: {p_wave.min():.0f} - {p_wave.max():.0f} m/s")
    print(f"  S-wave: {s_wave.min():.0f} - {s_wave.max():.0f} m/s")
    
    # Preprocess
    preprocessor = WellLogPreprocessor(normalization='minmax', feature_engineering=True)
    features_norm, targets_norm = preprocessor.fit_transform(
        features.reshape(-1, n_features), 
        targets.reshape(-1, 1),
        feature_names=config['data']['features'],
        target_names=config['data']['target']
    )
    
    features_norm = features_norm.reshape(n_samples, seq_len, -1)
    targets_norm = targets_norm.reshape(n_samples, seq_len, 1)
    
    print(f"After normalization:")
    print(f"  Features shape: {features_norm.shape}")
    print(f"  Normalized range: {features_norm.min():.3f} - {features_norm.max():.3f}")
    
    # Create models
    config['model']['params']['input_dim'] = features_norm.shape[-1]
    rock_physics_model = RockPhysicsFactory.create('mudrock_line')
    strategy = PhysicsGuidanceStrategy('loss_function')
    strategy_handler = StrategyHandler(strategy, rock_physics_model)
    model = BiGRU(**config['model']['params'])
    
    # Create trainer with preprocessor
    trainer = PhysicsGuidedTrainer(
        model=model,
        rock_physics_model=rock_physics_model,
        strategy_handler=strategy_handler,
        config=config,
        preprocessor=preprocessor
    )
    
    print(f"Trainer VP index: {trainer.vp_index}")
    
    # Test unit conversion manually
    features_tensor = torch.FloatTensor(features_norm[:3])
    vp_normalized = features_tensor[:, :, trainer.vp_index]
    
    print(f"VP normalized: {vp_normalized.min():.3f} - {vp_normalized.max():.3f}")
    
    # Test the trainer's unit conversion logic
    if trainer.preprocessor is not None:
        batch_size, seq_len = vp_normalized.shape
        vp_reshaped = vp_normalized.numpy().reshape(-1, 1)
        
        # Create dummy features for denormalization
        n_base_features = len(config['data']['features'])
        dummy_features = np.zeros((vp_reshaped.shape[0], n_base_features))
        dummy_features[:, trainer.vp_index] = vp_reshaped.flatten()
        
        # Denormalize
        vp_denorm = trainer.preprocessor.scalers['features'].inverse_transform(dummy_features)
        vp_physical_ms = vp_denorm[:, trainer.vp_index].reshape(batch_size, seq_len)
        vp_physics_kms = vp_physical_ms / 1000.0
        
        print(f"VP denormalized (m/s): {vp_physical_ms.min():.0f} - {vp_physical_ms.max():.0f}")
        print(f"VP for physics (km/s): {vp_physics_kms.min():.3f} - {vp_physics_kms.max():.3f}")
        
        # Test physics prediction
        physics_pred = rock_physics_model.predict(vp_physics_kms[0, :5])
        print(f"Physics prediction (km/s): {physics_pred.min():.3f} - {physics_pred.max():.3f}")
        print(f"Physics prediction (m/s): {(physics_pred * 1000).min():.0f} - {(physics_pred * 1000).max():.0f}")
    
    # Test BiGRU prediction and denormalization
    model.eval()
    with torch.no_grad():
        predictions_norm = model(features_tensor).numpy()
    
    predictions_phys = preprocessor.inverse_transform_targets(predictions_norm)
    targets_phys = preprocessor.inverse_transform_targets(targets_norm[:3])
    
    print(f"BiGRU predictions (m/s): {predictions_phys.min():.0f} - {predictions_phys.max():.0f}")
    print(f"Actual targets (m/s): {targets_phys.min():.0f} - {targets_phys.max():.0f}")
    
    # Check if ranges are reasonable
    pred_in_range = 200 < predictions_phys.max() < 6000
    physics_reasonable = 1000 < (physics_pred * 1000).max() < 5000
    
    print(f"\nResults:")
    print(f"✓ Unit conversion working: VP properly denormalized")
    print(f"✓ Physics predictions reasonable: {physics_reasonable}")
    print(f"✓ BiGRU predictions in range: {pred_in_range}")
    
    success = pred_in_range and physics_reasonable
    
    if success:
        print("\n🎉 Unit conversion fix is working correctly!")
    else:
        print("\n❌ Unit conversion still has issues")
    
    return success

if __name__ == "__main__":
    try:
        success = test_unit_conversion()
        if success:
            print("\n✅ Unit conversion test PASSED!")
        else:
            print("\n⚠️  Unit conversion test FAILED!")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
