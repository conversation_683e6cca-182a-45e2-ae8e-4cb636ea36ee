from .base import RockPhysicsModel
from .mudrock_line import MudrockLine
from .empirical_vpvs import EmpiricalVPVS
from .multiparameter import MultiparameterRegression

class RockPhysicsFactory:
    """Factory class for creating rock physics models."""

    _models = {
        "mudrock_line": MudrockLine,
        "empirical_vpvs": EmpiricalVPVS,
        "multiparameter": MultiparameterRegression,
        # "xu_white": XuWhiteModel,  # Add when implemented
    }

    @classmethod
    def create(cls, model_type: str, **kwargs) -> RockPhysicsModel:
        """
        Create a rock physics model instance.

        Args:
            model_type: Type of model to create
            **kwargs: Model-specific parameters

        Returns:
            Instance of requested rock physics model
        """
        if model_type not in cls._models:
            raise ValueError(f"Unknown model type: {model_type}")

        return cls._models[model_type](**kwargs)

    @classmethod
    def register(cls, name: str, model_class):
        """Register a new model type."""
        cls._models[name] = model_class
