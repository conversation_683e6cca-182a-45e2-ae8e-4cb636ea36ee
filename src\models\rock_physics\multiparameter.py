import numpy as np
from typing import Union, Dict, List, Any, Optional
from .base import RockPhysicsModel

class MultiparameterRegression(RockPhysicsModel):
    """Multiparameter regression for VS prediction.
    
    VS = a*GR + b*DEN + c*VP + d*RES + e
    
    Based on <PERSON> et al. (2024) Equation 7.
    This model uses multiple well log parameters to predict shear wave velocity,
    providing more comprehensive predictions than single-parameter models.
    """
    
    def __init__(self, coefficients: Optional[Dict[str, float]] = None, name: str = "multiparameter"):
        """
        Initialize the multiparameter regression model.
        
        Args:
            coefficients: Dict with keys 'GR', 'DEN', 'VP', 'RES', 'intercept'.
                         If None, uses default values that should be fitted to data.
            name: Model name identifier
        """
        # Default coefficients (these should be fitted to actual data)
        default_coefficients = {
            'GR': 0.002,      # Gamma ray coefficient (km/s per API)
            'DEN': 0.5,       # Density coefficient (km/s per g/cm³)
            'VP': 0.3,        # P-wave coefficient (dimensionless)
            'RES': -0.001,    # Resistivity coefficient (km/s per log10(ohm.m))
            'intercept': 1.0  # Intercept term (km/s)
        }
        
        self.coefficients = coefficients if coefficients is not None else default_coefficients.copy()
        super().__init__(name=name, **self.coefficients)
        
        # Feature names in expected order
        self.feature_names = ['GR', 'DEN', 'VP', 'RES']
        self._fitted = coefficients is not None  # True if custom coefficients provided
    
    def predict(self, features: Union[Dict[str, np.ndarray], np.ndarray], **kwargs) -> np.ndarray:
        """
        Predict VS from multiple well logs.
        
        Args:
            features: Input features. Can be:
                - Dict with keys matching feature_names, values in appropriate units:
                  * GR: API units
                  * DEN: g/cm³
                  * VP: km/s
                  * RES: ohm.m (will be log-transformed internally)
                - np.ndarray: Feature matrix with columns in order [GR, DEN, VP, RES]
                  Units should be: [API, g/cm³, km/s, ohm.m]
            **kwargs: Additional parameters (ignored)
                
        Returns:
            vs: S-wave velocity in km/s
            
        Raises:
            ValueError: If required features are missing or have invalid shapes
            TypeError: If features is neither dict nor ndarray
        """
        if isinstance(features, dict):
            return self._predict_from_dict(features)
        elif isinstance(features, np.ndarray):
            return self._predict_from_array(features)
        else:
            raise TypeError("Features must be either dict or numpy array")
    
    def _predict_from_dict(self, features_dict: Dict[str, np.ndarray]) -> np.ndarray:
        """Predict from dictionary input."""
        # Check required features
        missing_features = [f for f in self.feature_names if f not in features_dict]
        if missing_features:
            raise ValueError(f"Missing required features: {missing_features}")
        
        # Get feature arrays and ensure they're all the same length
        feature_arrays = []
        for feature in self.feature_names:
            arr = np.asarray(features_dict[feature]).flatten()
            feature_arrays.append(arr)
        
        # Check all arrays have same length
        lengths = [len(arr) for arr in feature_arrays]
        if len(set(lengths)) > 1:
            raise ValueError(f"All feature arrays must have same length. Got: {dict(zip(self.feature_names, lengths))}")
        
        # Start with intercept
        vs = np.full(lengths[0], self.coefficients['intercept'], dtype=float)
        
        # Add contributions from each feature
        for i, feature in enumerate(self.feature_names):
            values = feature_arrays[i]
            
            # Apply log transform to resistivity
            if feature == 'RES':
                values = np.log10(np.maximum(values, 1e-8))  # Avoid log(0)
            
            # Add contribution
            vs += self.coefficients[feature] * values
        
        return vs
    
    def _predict_from_array(self, features_array: np.ndarray) -> np.ndarray:
        """Predict from array input."""
        features_array = np.asarray(features_array)
        
        # Ensure 2D array
        if features_array.ndim == 1:
            if len(features_array) != len(self.feature_names):
                raise ValueError(f"1D input must have {len(self.feature_names)} features, got {len(features_array)}")
            features_array = features_array.reshape(1, -1)
        elif features_array.ndim == 2:
            if features_array.shape[1] != len(self.feature_names):
                raise ValueError(f"2D input must have {len(self.feature_names)} columns, got {features_array.shape[1]}")
        else:
            raise ValueError("Input array must be 1D or 2D")
        
        n_samples = features_array.shape[0]
        
        # Start with intercept
        vs = np.full(n_samples, self.coefficients['intercept'], dtype=float)
        
        # Add contributions from each feature
        for i, feature in enumerate(self.feature_names):
            values = features_array[:, i]
            
            # Apply log transform to resistivity
            if feature == 'RES':
                values = np.log10(np.maximum(values, 1e-8))  # Avoid log(0)
            
            # Add contribution
            vs += self.coefficients[feature] * values
        
        # Return scalar if single sample input
        if n_samples == 1:
            return vs[0]
        
        return vs
    
    def fit(self, features: Union[Dict[str, np.ndarray], np.ndarray], vs_target: np.ndarray, **kwargs) -> None:
        """
        Fit coefficients using multiple linear regression.
        
        Args:
            features: Input features in same format as predict()
            vs_target: Target S-wave velocity in km/s
            **kwargs: Additional fitting parameters
                - regularization: float, L2 regularization strength (default: 0.0)
                - feature_scaling: bool, whether to scale features (default: False)
        """
        regularization = kwargs.get('regularization', 0.0)
        feature_scaling = kwargs.get('feature_scaling', False)
        
        # Convert inputs to feature matrix
        if isinstance(features, dict):
            X = self._build_feature_matrix_from_dict(features)
        else:
            X = self._build_feature_matrix_from_array(features)
        
        # Flatten target
        y = np.asarray(vs_target).flatten()
        
        if len(y) != X.shape[0]:
            raise ValueError(f"Target length ({len(y)}) doesn't match features ({X.shape[0]})")
        
        # Optional feature scaling
        if feature_scaling:
            self.feature_means_ = np.mean(X, axis=0)
            self.feature_stds_ = np.std(X, axis=0)
            self.feature_stds_[self.feature_stds_ == 0] = 1.0  # Avoid division by zero
            X = (X - self.feature_means_) / self.feature_stds_
        
        # Add intercept column
        X_with_intercept = np.column_stack([X, np.ones(len(y))])
        
        # Solve with optional regularization
        if regularization > 0:
            # Ridge regression: (X'X + λI)⁻¹X'y
            XTX = X_with_intercept.T @ X_with_intercept
            XTX[:-1, :-1] += regularization * np.eye(len(self.feature_names))  # Don't regularize intercept
            XTy = X_with_intercept.T @ y
            coeffs = np.linalg.solve(XTX, XTy)
        else:
            # Ordinary least squares
            coeffs, residuals, rank, s = np.linalg.lstsq(X_with_intercept, y, rcond=None)
        
        # Update coefficients (apply inverse scaling if used)
        for i, feature in enumerate(self.feature_names):
            coeff = coeffs[i]
            if feature_scaling:
                coeff /= self.feature_stds_[i]  # Undo scaling
            self.coefficients[feature] = coeff
        
        # Update intercept
        intercept = coeffs[-1]
        if feature_scaling:
            # Adjust intercept for feature scaling
            intercept -= np.sum(self.coefficients[f] * self.feature_means_[i] for i, f in enumerate(self.feature_names))
        self.coefficients['intercept'] = intercept
        
        # Update parameters dict
        self.params.update(self.coefficients)
        self._fitted = True
        
        # Calculate fit quality metrics
        y_pred = self.predict(features)
        self.fit_rmse = np.sqrt(np.mean((y - y_pred)**2))
        self.fit_r2 = 1 - np.sum((y - y_pred)**2) / np.sum((y - np.mean(y))**2)
    
    def _build_feature_matrix_from_dict(self, features_dict: Dict[str, np.ndarray]) -> np.ndarray:
        """Build feature matrix from dictionary input."""
        # Check required features
        missing_features = [f for f in self.feature_names if f not in features_dict]
        if missing_features:
            raise ValueError(f"Missing required features: {missing_features}")
        
        # Build matrix
        feature_columns = []
        for feature in self.feature_names:
            values = np.asarray(features_dict[feature]).flatten()
            
            # Apply log transform to resistivity
            if feature == 'RES':
                values = np.log10(np.maximum(values, 1e-8))
                
            feature_columns.append(values)
        
        return np.column_stack(feature_columns)
    
    def _build_feature_matrix_from_array(self, features_array: np.ndarray) -> np.ndarray:
        """Build feature matrix from array input."""
        features_array = np.asarray(features_array)
        
        # Ensure 2D
        if features_array.ndim == 1:
            features_array = features_array.reshape(1, -1)
        
        if features_array.shape[1] != len(self.feature_names):
            raise ValueError(f"Array must have {len(self.feature_names)} columns")
        
        # Apply log transform to RES column (index 3)
        X = features_array.copy().astype(float)
        res_idx = self.feature_names.index('RES')
        X[:, res_idx] = np.log10(np.maximum(X[:, res_idx], 1e-8))
        
        return X
    
    def get_equation(self) -> str:
        """Return the model equation as a string."""
        terms = []
        for feature in self.feature_names:
            coeff = self.coefficients[feature]
            if feature == 'RES':
                terms.append(f"{coeff:.4f}×log10({feature})")
            else:
                terms.append(f"{coeff:.4f}×{feature}")
        
        equation = "VS = " + " + ".join(terms) + f" + {self.coefficients['intercept']:.4f}"
        return equation
    
    def get_fit_quality(self) -> Dict[str, Any]:
        """Return fitting quality metrics if available."""
        metrics = {'fitted': self._fitted}
        if hasattr(self, 'fit_rmse'):
            metrics.update({
                'rmse': self.fit_rmse,
                'r2': self.fit_r2
            })
        return metrics
    
    def get_feature_importance(self) -> Dict[str, float]:
        """Return the absolute coefficient values as a measure of feature importance."""
        importance = {}
        for feature in self.feature_names:
            importance[feature] = abs(self.coefficients[feature])
        return importance