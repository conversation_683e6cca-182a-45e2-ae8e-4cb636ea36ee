#!/usr/bin/env python3
"""
Simple test script to verify the new rock physics models implementation.
This script tests the basic functionality without requiring pytest.
"""

import sys
import os
import numpy as np

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_empirical_vpvs_model():
    """Test the EmpiricalVPVS model."""
    print("Testing EmpiricalVPVS model...")
    
    from models.rock_physics.empirical_vpvs import EmpiricalVPVS
    
    # Test initialization
    model = EmpiricalVPVS()
    print(f"  ✓ Initialized with default parameters: a={model.a:.4f}, b={model.b:.4f}")
    
    # Test array input prediction
    vp_values = np.array([3.0, 4.0, 5.0])  # km/s
    vs_pred = model.predict(vp_values)
    print(f"  ✓ Array prediction: VP={vp_values} -> VS={vs_pred}")
    
    # Test dict input prediction
    vs_pred_dict = model.predict({'VP': vp_values})
    assert np.allclose(vs_pred, vs_pred_dict), "Array and dict predictions should match"
    print(f"  ✓ Dict prediction matches array prediction")
    
    # Test basic sanity checks
    assert np.all(vs_pred > 0), "VS should be positive"
    assert np.all(vs_pred < vp_values), "VS should be less than VP"
    print(f"  ✓ Sanity checks passed (VS > 0, VS < VP)")
    
    # Test equation
    equation = model.get_equation()
    print(f"  ✓ Equation: {equation}")
    
    print("  EmpiricalVPVS model tests PASSED!\n")


def test_multiparameter_model():
    """Test the MultiparameterRegression model."""
    print("Testing MultiparameterRegression model...")
    
    from models.rock_physics.multiparameter import MultiparameterRegression
    
    # Test initialization
    model = MultiparameterRegression()
    print(f"  ✓ Initialized with feature names: {model.feature_names}")
    
    # Test dict input prediction
    features_dict = {
        'GR': np.array([40, 60, 80]),     # API
        'DEN': np.array([2.2, 2.4, 2.6]), # g/cm³
        'VP': np.array([3.0, 4.0, 5.0]),  # km/s
        'RES': np.array([10, 100, 1000])  # ohm.m
    }
    
    vs_pred_dict = model.predict(features_dict)
    print(f"  ✓ Dict prediction: VS={vs_pred_dict}")
    
    # Test array input prediction
    features_array = np.array([
        [40, 2.2, 3.0, 10],    # Sample 1
        [60, 2.4, 4.0, 100],   # Sample 2
        [80, 2.6, 5.0, 1000]   # Sample 3
    ])
    
    vs_pred_array = model.predict(features_array)
    print(f"  ✓ Array prediction: VS={vs_pred_array}")
    
    # Check consistency
    assert np.allclose(vs_pred_dict, vs_pred_array), "Dict and array predictions should match"
    print(f"  ✓ Dict and array predictions are consistent")
    
    # Test basic sanity checks
    assert np.all(vs_pred_dict > 0), "VS should be positive"
    assert np.all(np.isfinite(vs_pred_dict)), "VS should be finite"
    print(f"  ✓ Sanity checks passed (VS > 0, VS finite)")
    
    # Test equation
    equation = model.get_equation()
    print(f"  ✓ Equation: {equation}")
    
    # Test feature importance
    importance = model.get_feature_importance()
    print(f"  ✓ Feature importance: {importance}")
    
    print("  MultiparameterRegression model tests PASSED!\n")


def test_factory():
    """Test the RockPhysicsFactory."""
    print("Testing RockPhysicsFactory...")
    
    from models.rock_physics import RockPhysicsFactory
    
    # Test available models
    available_models = list(RockPhysicsFactory._models.keys())
    print(f"  ✓ Available models: {available_models}")
    
    # Test creating each model
    for model_name in ['mudrock_line', 'empirical_vpvs', 'multiparameter']:
        model = RockPhysicsFactory.create(model_name)
        print(f"  ✓ Created {model_name}: {model.__class__.__name__}")
        
        # Test basic functionality
        assert hasattr(model, 'predict'), f"{model_name} should have predict method"
        assert hasattr(model, 'fit'), f"{model_name} should have fit method"
        assert hasattr(model, 'name'), f"{model_name} should have name attribute"
    
    print("  RockPhysicsFactory tests PASSED!\n")


def test_backward_compatibility():
    """Test that old code still works with new interface."""
    print("Testing backward compatibility...")
    
    from models.rock_physics import RockPhysicsFactory
    
    # Test that MudrockLine still works with old VP-only interface
    mudrock_model = RockPhysicsFactory.create('mudrock_line')
    vp_values = np.array([3.0, 4.0, 5.0])
    
    # This should still work (backward compatibility)
    vs_old_style = mudrock_model.predict(vp_values)
    vs_new_style = mudrock_model.predict({'VP': vp_values})
    
    assert np.allclose(vs_old_style, vs_new_style), "Backward compatibility check failed"
    print(f"  ✓ MudrockLine backward compatibility maintained")
    
    # Test EmpiricalVPVS with both interfaces
    empirical_model = RockPhysicsFactory.create('empirical_vpvs')
    vs_emp_old = empirical_model.predict(vp_values)
    vs_emp_new = empirical_model.predict({'VP': vp_values})
    
    assert np.allclose(vs_emp_old, vs_emp_new), "EmpiricalVPVS compatibility check failed"
    print(f"  ✓ EmpiricalVPVS dual interface compatibility maintained")
    
    print("  Backward compatibility tests PASSED!\n")


def test_resistivity_log_transform():
    """Test that resistivity log transform works correctly."""
    print("Testing resistivity log transform...")
    
    from models.rock_physics.multiparameter import MultiparameterRegression
    
    # Create a model where only RES coefficient is non-zero
    model = MultiparameterRegression(coefficients={
        'GR': 0, 'DEN': 0, 'VP': 0, 'RES': 1.0, 'intercept': 0
    })
    
    # Test with RES = 100 (log10(100) = 2)
    features = {
        'GR': np.array([0]),
        'DEN': np.array([0]),
        'VP': np.array([0]),
        'RES': np.array([100])
    }
    
    vs_pred = model.predict(features)
    expected = np.log10(100)  # Should be 2.0
    
    assert np.allclose(vs_pred, expected), f"Expected {expected}, got {vs_pred}"
    print(f"  ✓ Resistivity log transform: RES=100 -> log10(RES)={expected:.1f}")
    
    print("  Resistivity log transform tests PASSED!\n")


def run_all_tests():
    """Run all tests."""
    print("=" * 60)
    print("TESTING NEW ROCK PHYSICS MODELS IMPLEMENTATION")
    print("=" * 60)
    
    try:
        test_empirical_vpvs_model()
        test_multiparameter_model()
        test_factory()
        test_backward_compatibility()
        test_resistivity_log_transform()
        
        print("=" * 60)
        print("🎉 ALL TESTS PASSED! 🎉")
        print("The new rock physics models are ready to use.")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        print(f"Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)