model:
  type: "BiGRU"
  params:
    input_dim: 4  # P-WAVE, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, RT
    hidden_dim: 16
    output_dim: 1
    num_layers: 1
    dropout: 0.1

rock_physics:
  model_type: "mudrock_line"
  params:
    a: 1.16
    b: 1.36

training:
  strategy: "loss_function"  # Use loss_function for simpler testing
  batch_size: 16
  learning_rate: 0.001
  epochs: 5  # Very short for testing
  early_stopping_patience: 3

data:
  features: ["P-WAVE", "RHOB", "PHIE", "RT"]
  target: ["S-WAVE"]
  vp_feature_name: "P-WAVE"
  train_test_split: 0.8
  normalization: "standard"
  sequence_length: 20  # Shorter sequences for faster testing
  sequence_stride: 5

physics_coupling:
  vp_units: "m/s"
  physics_units: "km/s"
  conversion_factor: 0.001

# Transfer learning configuration (for transfer learning pipeline)
transfer_learning:
  enabled: false  # Disabled for quick testing
  pretrain_epochs: 2
  pretrain_lr: 0.001
  finetune_epochs: 3
  finetune_lr: 0.0001
  early_stopping_patience: 2
  save_pretrained: true
  pretrained_path: "output/models/pretrained_bigru.pth"