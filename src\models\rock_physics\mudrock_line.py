import numpy as np
from typing import Optional
from .base import RockPhysicsModel

class MudrockLine(RockPhysicsModel):
    """
    Mudrock line model for VS prediction.
    VP = a × VS + b, where default values are a=1.16, b=1.36
    """

    def __init__(self, a: float = 1.16, b: float = 1.36, name: str = "mudrock_line"):
        super().__init__(name=name, a=a, b=b)
        self.a = a
        self.b = b
        self._fitted = True  # Uses fixed coefficients by default

    def predict(self, features, **kwargs) -> np.ndarray:
        """
        Predict S-wave velocity from P-wave velocity.

        Args:
            features: P-wave velocity data. Can be:
                - np.ndarray: Direct VP values in km/s
                - Dict: Must contain 'VP' key with velocity values in km/s

        Returns:
            Predicted S-wave velocity in km/s
        """
        # Handle different input types for backward compatibility
        if isinstance(features, dict):
            if 'VP' not in features:
                raise ValueError("Dictionary input must contain 'VP' key")
            vp = np.asarray(features['VP'])
        else:
            # Assume it's VP values directly (backward compatibility)
            vp = np.asarray(features)
            
        return (vp - self.b) / self.a

    def fit(self, features, vs: np.ndarray, **kwargs) -> None:
        """
        Optionally fit coefficients from data using linear regression.

        Args:
            features: P-wave velocity training data. Same format as predict()
            vs: S-wave velocity training data
        """
        from sklearn.linear_model import LinearRegression

        # Extract VP from features
        if isinstance(features, dict):
            if 'VP' not in features:
                raise ValueError("Dictionary input must contain 'VP' key")
            vp = np.asarray(features['VP'])
        else:
            vp = np.asarray(features)

        model = LinearRegression()
        model.fit(vs.reshape(-1, 1), vp.reshape(-1, 1))

        self.a = model.coef_[0, 0]
        self.b = model.intercept_[0]
        self._fitted = True

    def get_equation(self) -> str:
        """Return the model equation as a string."""
        return f"VP = {self.a:.3f} × VS + {self.b:.3f}"
