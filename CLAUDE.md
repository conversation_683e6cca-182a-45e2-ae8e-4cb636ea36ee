# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **Physics-Guided Machine Learning framework** for predicting S-wave velocity (VS) from conventional well logs, based on <PERSON> et al. (2024). The framework implements a BiGRU neural network with physics-based constraints using rock physics models to improve prediction accuracy on geophysical well data.

**Implementation Status**: Phase 2.2 Complete (~96% alignment with <PERSON> et al. 2024)
- ✅ Unit consistency fixes (VP units m/s → km/s conversion)
- ✅ Rock physics models (mudrock line, empirical VP-VS, multiparameter)
- ✅ Two-stage transfer learning (pretrain on physics → finetune on data)
- ✅ Comprehensive pipeline testing with real LAS data

## Key Commands

### Primary Pipeline Tests (Phase 2.2 Complete)
```bash
# MANDATORY: Complete LAS to ML pipeline test (main implementation)
python test_las_ml_pipeline.py

# MANDATORY: Advanced pipeline with transfer learning (Phase 2.2 feature)
python test_las_transfer_learning_pipeline.py

# Alternative: Configurable training with synthetic data
python examples/train_model.py
```

### Legacy/Demo Scripts (Moved to archives/)
```bash
# Historical demo (now in archives/ - use main pipeline tests instead)
# python run_las_demo.py
```

### Unit Testing
```bash
# Run unit tests
pytest tests/

# Run specific test modules
pytest tests/test_rock_physics.py
pytest tests/test_training.py
pytest tests/test_new_rock_physics_models.py

# Unit test for RMSE improvements
pytest tests/test_rmse_improvement_synthetic.py
```

### Code Quality
```bash
# Format code
black src/ tests/ examples/ *.py

# Lint code
flake8 src/ tests/ examples/ *.py
```

## Architecture Overview

### Core Framework Design
The codebase follows a **modular, physics-guided ML architecture** with three main pillars:

1. **Data Pipeline** (`src/data/`): LAS file parsing, preprocessing, and sequence generation
2. **Physics Models** (`src/models/rock_physics/`): Rock physics constraints (mudrock line, empirical VP-VS relationships)
3. **Training Infrastructure** (`src/training/`): Physics-guided training strategies including pseudolabeling, loss function coupling, and transfer learning

### Key Architectural Patterns

**Factory Pattern for Physics Models**: Use `RockPhysicsFactory.create()` to instantiate physics models. All models inherit from `RockPhysicsModel` base class.

**Strategy Pattern for Physics Guidance**: The framework supports three guidance strategies:
- `pseudolabels`: Add physics predictions as additional input features
- `loss_function`: Combine ML loss with physics-based loss terms
- `transfer_learning`: Two-stage training (pretrain on physics targets → finetune on true targets)

**Configuration-Driven Design**: All model parameters, training strategies, and data processing options are controlled via `configs/default_config.yaml`.

### Critical Implementation Details

**Unit Consistency**: Physics models expect VP in km/s, but data is typically in m/s. The trainer handles conversion using `physics_coupling.conversion_factor: 0.001` in config.

**Sequence Processing**: BiGRU operates on sequences of well log measurements. Default sequence length is 100 samples with stride 10 for overlapping windows.

**Data Normalization**: Uses standard scaling (mean=0, std=1) by default. Physics coupling requires proper denormalization before physics model calls and renormalization afterward.

## File Structure Focus

### Current File Organization (Phase 2.2)

**Mandatory Pipeline Tests (Root Directory)**:
- `test_las_ml_pipeline.py`: Complete LAS to ML pipeline test (main implementation)
- `test_las_transfer_learning_pipeline.py`: Advanced pipeline with transfer learning

**Important Documentation (Root Directory)**:
- `REALISTIC_IMPLEMENTATION_GUIDE.md`: Primary implementation guide and progress tracker
- `Implementation_Analysis.md`: Strategic analysis document
- `Implementation_Plan.md`: Detailed implementation roadmap
- `Implementation_Plan_Simple.md`: Quick reference guide
- `FILE_ORGANIZATION_SUMMARY.md`: Repository map and file organization

**Historical/Debug Files**: Moved to `archives/` directory
- Phase 1 testing files (`test_unit_conversion*.py`, `test_scaling_fix*.py`)
- Phase 2 component tests (`test_transfer_learning.py`, `test_rock_physics_implementation.py`)
- Demo scripts (`demo_new_rock_physics_models.py`, `run_las_demo.py`)

### Most Important Files for Development
- `src/training/trainer.py`: Core training orchestrator with physics coupling logic
- `src/models/neural_networks.py`: BiGRU architecture implementation
- `src/data/las_loader.py`: LAS file parsing and well log data extraction
- `src/models/rock_physics/`: All physics models (mudrock_line, empirical_vpvs, multiparameter)
- `configs/default_config.yaml`: Central configuration for all components

### Entry Points for Different Use Cases
- **Main pipeline test**: `test_las_ml_pipeline.py`
- **Transfer learning test**: `test_las_transfer_learning_pipeline.py`
- **Configurable training**: `examples/train_model.py`

## Data Conventions

### LAS File Processing
The framework expects LAS files with specific curve names (supports common variations):
- **P-WAVE**: VP, DTCO, DTC, P-WAVE (compressional velocity)
- **S-WAVE**: VS, DTSM, DTS, S-WAVE (shear velocity, target)
- **RHOB**: RHOB, DEN, DENSITY (bulk density)
- **PHIE**: PHIE, NPHI, POROSITY (porosity)
- **RT**: RT, RES, RESISTIVITY (resistivity)

### Configuration Patterns
When modifying `configs/default_config.yaml`:
- `data.features` must match available curves in LAS files
- `data.vp_feature_name` should point to the VP curve name for physics coupling
- `physics_coupling.conversion_factor: 0.001` converts m/s to km/s for physics models
- `data.sequence_length: 100` and `data.sequence_stride: 10` control sequence windowing

## Physics Model Integration

### Adding New Physics Models
1. Create new class in `src/models/rock_physics/` inheriting from `RockPhysicsModel`
2. Implement `predict()`, `fit()`, and `get_parameters()` methods
3. Register in `RockPhysicsFactory` in `src/models/rock_physics/__init__.py`
4. Add configuration entry in `configs/default_config.yaml`

### Available Physics Models (Phase 2.2)
- **mudrock_line**: Castagna mudrock line (VP-VS relationship) - Equation 5
- **empirical_vpvs**: Empirical VP-VS correlations - Equation 6
- **multiparameter**: Multi-parameter regression model - Equation 7 (uses GR, density, VP, resistivity)

## Training Strategy Guidelines (Phase 2.2 Implementation)

### Transfer Learning (Recommended - Phase 2.2 Feature)
```yaml
training:
  strategy: "transfer_learning"
transfer_learning:
  enabled: true
  pretrain_epochs: 50      # Stage 1: pretrain on physics targets
  finetune_epochs: 100     # Stage 2: finetune on true targets
```
This implements the two-stage approach from Zhao et al. (2024): pretrain on physics-derived targets, then finetune on true targets.

### Alternative Strategies
- **Physics Loss Function**: `training.strategy: "loss_function"` - combines ML predictions with physics constraints
- **Pseudolabels**: `training.strategy: "pseudolabels"` - adds physics predictions as input features

## Testing Philosophy and Current Implementation

### Primary Pipeline Tests (Phase 2.2)
1. **`test_las_ml_pipeline.py`**: Complete end-to-end pipeline with real LAS data
2. **`test_las_transfer_learning_pipeline.py`**: Enhanced pipeline with transfer learning implementation

### Implementation Status
- **Phase 1**: Unit consistency, RES log transforms, physics coupling ✅
- **Phase 2.1**: Rock physics models (EmpiricalVPVS, MultiparameterRegression) ✅
- **Phase 2.2**: Two-stage transfer learning implementation ✅
- **Alignment**: ~96% with Zhao et al. (2024) methodology

The framework emphasizes **comprehensive testing with real geophysical data** through the mandatory pipeline tests, ensuring complete functionality from LAS file loading through model training and evaluation.