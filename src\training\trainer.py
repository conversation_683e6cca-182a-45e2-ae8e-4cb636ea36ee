import torch
import torch.nn as nn
from torch.utils.data import <PERSON><PERSON>oader
from typing import Dict, Any, Optional
import numpy as np
from tqdm import tqdm
from .strategies import StrategyHandler
try:
    from ..utils.physics_coupling import PhysicsCouplingManager
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from utils.physics_coupling import PhysicsCouplingManager

class PhysicsGuidedTrainer:
    """
    Trainer for physics-guided neural networks.
    """

    def __init__(
        self,
        model: nn.Module,
        rock_physics_model,
        strategy_handler,
        config: Dict[str, Any],
        preprocessor=None,
        device: str = "cuda" if torch.cuda.is_available() else "cpu"
    ):
        self.model = model.to(device)
        self.rock_physics_model = rock_physics_model
        self.strategy_handler = strategy_handler
        self.device = device
        self.config = config
        self.preprocessor = preprocessor

        # Get VP feature index from config
        feature_names = config["data"]["features"]
        vp_feature_name = config["data"].get("vp_feature_name", "P-WAVE")
        self.vp_index = feature_names.index(vp_feature_name)

        # Initialize physics coupling manager
        self.physics_coupling = PhysicsCouplingManager(
            config=config,
            vp_index=self.vp_index,
            rock_physics_model=rock_physics_model
        )

        # Update strategy handler with physics coupling manager
        if hasattr(strategy_handler, 'physics_coupling'):
            strategy_handler.physics_coupling = self.physics_coupling

    def train_epoch(
        self,
        dataloader: DataLoader,
        optimizer: torch.optim.Optimizer,
        loss_fn: nn.Module
    ) -> float:
        """Train for one epoch."""
        self.model.train()
        total_loss = 0.0

        for batch in tqdm(dataloader, desc="Training"):
            features, targets = batch
            features = features.to(self.device)
            targets = targets.to(self.device)

            # Forward pass
            predictions = self.model(features)

            # Calculate physics predictions if needed
            physics_pred = None
            if self.strategy_handler.strategy.value == "loss_function":
                # Use centralized physics coupling manager
                physics_pred_np = self.physics_coupling.compute_physics_predictions(
                    features, self.preprocessor
                )

                # Convert to tensor with proper shape
                if physics_pred_np.ndim == 2:  # [B, T]
                    physics_pred_tensor = torch.tensor(
                        physics_pred_np,
                        device=self.device,
                        dtype=features.dtype
                    ).unsqueeze(-1)  # [B, T, 1]
                else:  # Already [B, T, 1]
                    physics_pred_tensor = torch.tensor(
                        physics_pred_np,
                        device=self.device,
                        dtype=features.dtype
                    )

                physics_pred = physics_pred_tensor

            # Calculate loss
            if self.strategy_handler.strategy.value == "loss_function":
                loss = loss_fn(predictions, targets, physics_pred)
            else:
                loss = loss_fn(predictions, targets)

            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

            total_loss += loss.item()

        return total_loss / len(dataloader)

    def evaluate(
        self,
        dataloader: DataLoader,
        loss_fn: nn.Module
    ) -> Dict[str, float]:
        """Evaluate model performance."""
        self.model.eval()
        total_loss = 0.0
        predictions_list = []
        targets_list = []

        with torch.no_grad():
            for batch in dataloader:
                features, targets = batch
                features = features.to(self.device)
                targets = targets.to(self.device)

                predictions = self.model(features)
                loss = loss_fn(predictions, targets)

                total_loss += loss.item()
                predictions_list.append(predictions.cpu().numpy())
                targets_list.append(targets.cpu().numpy())

        # Calculate metrics
        predictions = np.concatenate(predictions_list)
        targets = np.concatenate(targets_list)

        rmse = np.sqrt(np.mean((predictions - targets) ** 2))
        correlation = np.corrcoef(predictions.flatten(), targets.flatten())[0, 1]

        return {
            "loss": total_loss / len(dataloader),
            "rmse": rmse,
            "correlation": correlation
        }
    
    def compute_physics_predictions(self, features, preprocessor=None):
        """Compute physics-based predictions for transfer learning.

        Args:
            features: Input features tensor [B, T, F]
            preprocessor: Data preprocessor for unit conversion (optional)

        Returns:
            Physics-based VS predictions in same units as targets
        """
        # Use centralized physics coupling manager
        return self.physics_coupling.compute_physics_predictions(features, preprocessor)


