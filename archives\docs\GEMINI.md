# Project Overview

This project is a Python-based framework for physics-guided machine learning, specifically designed for predicting S-wave velocity (VS) from conventional well logs. It implements the architecture and methods described by <PERSON> et al. (2024), utilizing a Bidirectional GRU (BiGRU) neural network augmented with rock physics models.

The framework is modular, allowing for the extension of rock physics models and training strategies. It processes well log data from LAS (Log ASCII Standard) files, preprocesses the data, and trains a BiGRU model to predict S-wave velocity.

## Key Technologies

*   **Programming Language:** Python
*   **Core Libraries:**
    *   `torch`: For building and training the neural network.
    *   `pandas` & `numpy`: For data manipulation and numerical operations.
    *   `scikit-learn`: For data preprocessing and machine learning utilities.
    *   `matplotlib` & `seaborn`: For data visualization.
    *   `pyyaml`: For managing configurations.
*   **Model Architecture:** Bidirectional Gated Recurrent Unit (BiGRU).
*   **Physics Guidance:** Incorporates rock physics models (e.g., the mudrock line model) to guide the machine learning predictions.

# Building and Running

## Installation

1.  **Clone the repository.**
2.  **Create and activate a virtual environment:**
    ```bash
    python -m venv venv
    source venv/bin/activate  # On Windows: venv\Scripts\activate
    ```
3.  **Install dependencies:**
    ```bash
    pip install -r requirements.txt
    ```

## Running the Application

There are three main entry points for running the project:

1.  **Quick Demo (`run_las_demo.py`):**
    This script provides a quick demonstration of the entire pipeline using the sample LAS files included in the `Las/` directory. It loads the data, trains the model, and generates a set of output files, including visualizations and model weights.

    ```bash
    python run_las_demo.py
    ```

2.  **End-to-End Pipeline Test (`test_las_ml_pipeline.py`):**
    This is a more comprehensive script that runs the full training and evaluation pipeline. It performs detailed analysis, generates extensive visualizations, and saves the results.

    ```bash
    python test_las_ml_pipeline.py
    ```

3.  **Configurable Training Example (`examples/train_model.py`):**
    This script demonstrates how to run the training pipeline with custom configurations. It uses synthetic data and can be adapted for more advanced use cases.

    ```bash
    python examples/train_model.py
    ```

## Testing

The project includes a suite of tests to ensure the correctness of the individual components and the overall pipeline.

*   **Unit Tests:**
    ```bash
    pytest tests/
    ```
*   **End-to-End Pipeline Test:**
    ```bash
    python test_las_ml_pipeline.py
    ```

# Development Conventions

*   **Configuration:** Project parameters are managed through a central configuration file, `configs/default_config.yaml`. This includes settings for the model architecture, training strategy, and data processing.
*   **Modularity:** The code is organized into distinct modules for data loading, modeling, and training, promoting extensibility.
*   **Physics-Guided Approach:** The framework is designed to integrate rock physics models into the machine learning workflow. New physics models can be added by extending the `RockPhysicsModel` base class.
*   **Data Handling:** The `LASLoader` class is responsible for parsing and loading well log data from LAS files. The `WellLogDataProcessor` class handles data cleaning, preprocessing, and feature engineering.
*   **Training:** The `PhysicsGuidedTrainer` class orchestrates the model training process, incorporating different physics-guidance strategies.
*   **Visualization:** The pipeline generates a variety of plots to visualize the results, including prediction comparisons, residual analysis, and training history.
*   **Code Style:** The project uses `black` for code formatting and `flake8` for linting to maintain a consistent code style.
