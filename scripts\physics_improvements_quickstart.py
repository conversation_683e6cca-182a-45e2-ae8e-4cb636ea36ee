#!/usr/bin/env python
"""
Quick-start script for implementing physics improvements
This script demonstrates the key changes needed to improve alignment with <PERSON> et al. (2024)
"""

import os
import sys
import yaml
import numpy as np
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

def create_improved_config():
    """Create an improved configuration with all fixes enabled"""
    
    config = {
        'data': {
            'features': ['VP', 'GR', 'DEN', 'RES'],
            'target': 'VS',
            'sequence_length': 50,
            'overlap': 25
        },
        
        # NEW: Physics coupling with proper unit handling
        'physics_coupling': {
            'vp_feature_name': 'VP',
            'conversion_factor': 1000.0,  # m/s to km/s
            'expected_vp_range': [1500, 6000],  # m/s for validation
            'expected_vs_range': [500, 4000]   # m/s for validation
        },
        
        # NEW: Preprocessing with log transform
        'preprocessing': {
            'normalization_range': [-1, 1],
            'log_transform_features': ['RES'],  # Apply log10 to resistivity
            'outlier_std_threshold': 4.0
        },
        
        # NEW: Transfer learning configuration
        'transfer_learning': {
            'enabled': True,  # Enable two-stage training
            'pretrain_epochs': 50,
            'pretrain_lr': 0.001,
            'finetune_epochs': 100,
            'finetune_lr': 0.0001,
            'early_stopping_patience': 10,
            'save_pretrained': True,
            'pretrained_path': 'output/models/pretrained_bigru.pth'
        },
        
        # Model configuration (unchanged)
        'model': {
            'type': 'bigru',
            'input_dim': 4,
            'hidden_dim': 16,
            'num_layers': 1,
            'dropout': 0.2,
            'bidirectional': True
        },
        
        # Training configuration with physics guidance
        'training': {
            'batch_size': 32,
            'learning_rate': 0.001,
            'epochs': 150,
            'early_stopping_patience': 20,
            'physics_guidance_strategy': 'loss_function',
            'rock_physics_model': 'mudrock_line'  # Can change to 'empirical_vpvs' or 'multiparameter'
        },
        
        # Evaluation configuration
        'evaluation': {
            'metrics': ['rmse', 'r2', 'correlation'],
            'save_predictions': True,
            'plot_results': True
        }
    }
    
    return config

def demonstrate_unit_conversion():
    """Demonstrate proper unit conversion for physics models"""
    
    print("\n=== Unit Conversion Demonstration ===")
    
    # Simulated normalized VP data
    vp_normalized = np.array([-0.5, 0.0, 0.5])  # Normalized [-1, 1]
    
    # Simulated scaler parameters (MinMax normalization)
    vp_min = 1500  # m/s
    vp_max = 6000  # m/s
    
    # Denormalize to physical units (m/s)
    vp_ms = vp_normalized * (vp_max - vp_min) / 2 + (vp_max + vp_min) / 2
    print(f"Normalized VP: {vp_normalized}")
    print(f"VP in m/s: {vp_ms}")
    
    # Convert to km/s for physics models
    vp_kms = vp_ms / 1000.0
    print(f"VP in km/s: {vp_kms}")
    
    # Apply mudrock line: VS = 1.16 * VP + 1.36 (in km/s)
    vs_kms = 1.16 * vp_kms + 1.36
    print(f"VS from mudrock (km/s): {vs_kms}")
    
    # Convert back to m/s
    vs_ms = vs_kms * 1000.0
    print(f"VS in m/s: {vs_ms}")
    
    # Verify physical constraints
    vp_vs_ratio = vp_ms / vs_ms
    print(f"VP/VS ratio: {vp_vs_ratio}")
    print(f"Physical constraint satisfied (VP/VS > 1.4): {np.all(vp_vs_ratio > 1.4)}")

def demonstrate_log_transform():
    """Demonstrate log transform for resistivity"""
    
    print("\n=== Log Transform Demonstration ===")
    
    # Typical resistivity values (ohm.m)
    res_original = np.array([1, 10, 100, 1000])
    print(f"Original RES values: {res_original}")
    
    # Apply log10 transform
    res_log = np.log10(res_original + 1e-8)  # Add epsilon to avoid log(0)
    print(f"Log10(RES) values: {res_log}")
    
    # Show the compression effect
    print(f"Original range: {res_original.max() - res_original.min()}")
    print(f"Log range: {res_log.max() - res_log.min()}")
    print(f"Compression ratio: {(res_original.max() - res_original.min()) / (res_log.max() - res_log.min()):.1f}x")

def demonstrate_rock_physics_models():
    """Demonstrate different rock physics models"""
    
    print("\n=== Rock Physics Models Demonstration ===")
    
    vp_kms = np.array([2.0, 3.0, 4.0, 5.0])  # km/s
    
    # 1. Mudrock Line (Castagna et al., 1985)
    vs_mudrock = 1.16 * vp_kms + 1.36
    print(f"VP (km/s): {vp_kms}")
    print(f"VS Mudrock Line: {vs_mudrock}")
    
    # 2. Empirical VP-VS (power law)
    a, b = 0.8619, 0.8621  # Default from paper
    vs_empirical = a * np.power(vp_kms, b)
    print(f"VS Empirical: {vs_empirical}")
    
    # 3. Multiparameter (simplified example)
    # VS = 0.3*VP + 0.002*GR + 0.5*DEN - 0.001*log10(RES) + 1.0
    gr = 60  # API units
    den = 2.4  # g/cm³
    res_log = 2.0  # log10(100 ohm.m)
    
    vs_multi = 0.3 * vp_kms + 0.002 * gr + 0.5 * den - 0.001 * res_log + 1.0
    print(f"VS Multiparameter: {vs_multi}")
    
    # Compare models
    print("\nModel Comparison:")
    print(f"Mudrock vs Empirical diff: {np.mean(np.abs(vs_mudrock - vs_empirical)):.3f} km/s")
    print(f"Mudrock vs Multi diff: {np.mean(np.abs(vs_mudrock - vs_multi)):.3f} km/s")

def create_validation_plots():
    """Create plots to validate physics improvements"""
    
    try:
        import matplotlib.pyplot as plt
    except ImportError:
        print("Matplotlib not available, skipping plots")
        return
    
    print("\n=== Creating Validation Plots ===")
    
    # Create output directory
    os.makedirs('output/validation', exist_ok=True)
    
    # 1. Unit Conversion Validation
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # Normalized to physical units
    normalized = np.linspace(-1, 1, 100)
    vp_min, vp_max = 1500, 6000
    physical = normalized * (vp_max - vp_min) / 2 + (vp_max + vp_min) / 2
    
    ax1.plot(normalized, physical)
    ax1.set_xlabel('Normalized VP [-1, 1]')
    ax1.set_ylabel('Physical VP (m/s)')
    ax1.set_title('Denormalization Function')
    ax1.grid(True)
    
    # Physics model comparison
    vp_kms = np.linspace(1.5, 6.0, 100)
    vs_mudrock = 1.16 * vp_kms + 1.36
    vs_empirical = 0.8619 * np.power(vp_kms, 0.8621)
    
    ax2.plot(vp_kms, vs_mudrock, label='Mudrock Line', linewidth=2)
    ax2.plot(vp_kms, vs_empirical, label='Empirical VP-VS', linewidth=2, linestyle='--')
    ax2.plot(vp_kms, vp_kms/1.73, label='VP/VS=1.73', linewidth=1, linestyle=':')
    ax2.set_xlabel('VP (km/s)')
    ax2.set_ylabel('VS (km/s)')
    ax2.set_title('Rock Physics Models')
    ax2.legend()
    ax2.grid(True)
    
    plt.tight_layout()
    plt.savefig('output/validation/physics_models_comparison.png', dpi=150)
    plt.close()
    
    # 2. Log Transform Effect
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    res = np.logspace(0, 4, 100)  # 1 to 10000 ohm.m
    res_log = np.log10(res)
    
    ax1.plot(res, res, label='Linear Scale')
    ax1.set_xscale('log')
    ax1.set_yscale('log')
    ax1.set_xlabel('Resistivity (ohm.m)')
    ax1.set_ylabel('Resistivity (ohm.m)')
    ax1.set_title('Original Scale')
    ax1.grid(True, which="both", ls="-", alpha=0.2)
    
    ax2.plot(res, res_log)
    ax2.set_xscale('log')
    ax2.set_xlabel('Resistivity (ohm.m)')
    ax2.set_ylabel('log10(Resistivity)')
    ax2.set_title('After Log Transform')
    ax2.grid(True)
    
    plt.tight_layout()
    plt.savefig('output/validation/log_transform_effect.png', dpi=150)
    plt.close()
    
    print("Validation plots saved to output/validation/")

def main():
    """Main demonstration function"""
    
    print("=" * 60)
    print("Physics Improvements Quick-Start Demonstration")
    print("=" * 60)
    
    # 1. Create improved configuration
    print("\n1. Creating improved configuration...")
    config = create_improved_config()
    
    # Save config
    os.makedirs('output', exist_ok=True)
    with open('output/improved_config.yaml', 'w') as f:
        yaml.dump(config, f, default_flow_style=False)
    print("   Saved to output/improved_config.yaml")
    
    # 2. Demonstrate key concepts
    demonstrate_unit_conversion()
    demonstrate_log_transform()
    demonstrate_rock_physics_models()
    
    # 3. Create validation plots
    create_validation_plots()
    
    # 4. Print implementation checklist
    print("\n" + "=" * 60)
    print("IMPLEMENTATION CHECKLIST")
    print("=" * 60)
    
    checklist = """
Phase 1: Critical Fixes (This Week)
[ ] 1. Update configs/default_config.yaml with physics_coupling section
[ ] 2. Implement inverse_transform_single_feature in preprocessing.py
[ ] 3. Update compute_physics_predictions in trainer.py
[ ] 4. Add log transform support to preprocessing.py
[ ] 5. Run this script to verify concepts
[ ] 6. Run pytest to ensure no regressions

Phase 2: Core Methodology (Next 2 Weeks)
[ ] 1. Create src/models/rock_physics/empirical_vpvs.py
[ ] 2. Create src/models/rock_physics/multiparameter.py
[ ] 3. Register new models in rock_physics/__init__.py
[ ] 4. Create src/training/transfer_learning.py
[ ] 5. Update examples/train_model.py for transfer learning
[ ] 6. Run A/B tests with/without improvements

Phase 3: Validation (Week 4)
[ ] 1. Run full pipeline on all LAS files
[ ] 2. Compare metrics before/after improvements
[ ] 3. Create performance comparison plots
[ ] 4. Update documentation
[ ] 5. Create example notebooks
"""
    
    print(checklist)
    
    print("\n" + "=" * 60)
    print("Next Steps:")
    print("1. Review output/improved_config.yaml")
    print("2. Check output/validation/ for plots")
    print("3. Start with Phase 1 implementation")
    print("4. Use this script as reference for unit conversions")
    print("=" * 60)

if __name__ == "__main__":
    main()