"""
Blind Test Protocol Implementation

This module implements the blind test protocol from <PERSON> et al. (2024) where:
- One well is used for training (B-L-15_RP_INPUT.las)
- Four wells are used for testing (B-G-10, B-G-6, B-L-2.G1, EB-1)
- Per-well and aggregated metrics are calculated
- Statistical analysis and cross-well comparison are provided
"""
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
import torch
from torch.utils.data import DataLoader, TensorDataset
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import json
from datetime import datetime


class BlindTestProtocol:
    """
    Implements blind test protocol for LAS ML pipelines.

    Training well: B-L-15_RP_INPUT.las
    Test wells: B-G-10_RP_INPUT.las, B-G-6_RP_INPUT.las, B-L-2.G1_RP_INPUT.las, EB-1_RP_INPUT.las
    """

    def __init__(self, las_directory: str, config: dict, output_dir: Path):
        self.las_directory = Path(las_directory)
        self.config = config
        self.output_dir = Path(output_dir)

        # Define training and test wells
        self.training_well = "B-L-15_RP_INPUT.las"
        self.test_wells = [
            "B-G-10_RP_INPUT.las",
            "B-G-6_RP_INPUT.las",
            "B-L-2.G1_RP_INPUT.las",
            "EB-1_RP_INPUT.las"
        ]

        # Results storage
        self.training_data = {}
        self.test_data = {}
        self.results = {}
        self.metrics = {}

        # Setup output directories
        self._setup_directories()

    def _setup_directories(self):
        """Create blind test specific output directories."""
        self.blind_test_dir = self.output_dir / "blind_test_results"
        self.blind_test_dir.mkdir(parents=True, exist_ok=True)

        (self.blind_test_dir / "models").mkdir(exist_ok=True)
        (self.blind_test_dir / "visualizations").mkdir(exist_ok=True)
        (self.blind_test_dir / "visualizations" / "individual_wells").mkdir(exist_ok=True)
        (self.blind_test_dir / "results").mkdir(exist_ok=True)

        print(f"Blind test directories created at: {self.blind_test_dir}")

    def process_wells_for_blind_test(self, processor) -> Tuple[Dict, Dict]:
        """
        Process wells separately for blind test protocol.

        Returns:
            training_data: Dict with training well data
            test_data: Dict with test wells data
        """
        print("\nProcessing wells for blind test protocol...")
        print(f"   Training well: {self.training_well}")
        print(f"   Test wells: {', '.join(self.test_wells)}")

        # Process training well
        training_file = self.las_directory / self.training_well
        if not training_file.exists():
            raise FileNotFoundError(f"Training well not found: {training_file}")

        print(f"\nProcessing training well: {self.training_well}")
        try:
            train_features, train_targets = processor.process_single_well(str(training_file))
            if train_features is None or train_targets is None or len(train_features) == 0:
                raise ValueError(f"No valid data extracted from training well: {self.training_well}")

            self.training_data = {
                'features': train_features,
                'targets': train_targets,
                'well_name': self.training_well,
                'data_points': len(train_features)
            }
            print(f"   Training data: {len(train_features)} points")

        except Exception as e:
            raise RuntimeError(f"Failed to process training well {self.training_well}: {e}")

        # Process test wells
        print(f"\nProcessing test wells...")
        self.test_data = {}
        successful_test_wells = []

        for test_well in self.test_wells:
            test_file = self.las_directory / test_well
            if not test_file.exists():
                print(f"   Test well not found: {test_well}")
                continue

            try:
                test_features, test_targets = processor.process_single_well(str(test_file))
                if test_features is not None and test_targets is not None and len(test_features) > 0:
                    self.test_data[test_well] = {
                        'features': test_features,
                        'targets': test_targets,
                        'well_name': test_well,
                        'data_points': len(test_features)
                    }
                    successful_test_wells.append(test_well)
                    print(f"   {test_well}: {len(test_features)} points")
                else:
                    print(f"   {test_well}: No valid data extracted")

            except Exception as e:
                print(f"   {test_well}: Error processing - {e}")

        if len(successful_test_wells) == 0:
            raise RuntimeError("No test wells could be processed successfully")

        print(f"\nBlind test data summary:")
        print(f"   Training well: {self.training_data['data_points']} points")
        print(f"   Test wells: {len(successful_test_wells)} wells, {sum(data['data_points'] for data in self.test_data.values())} total points")

        return self.training_data, self.test_data

    def prepare_training_sequences(self, sequence_length: int, stride: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        Create sequences from training well data only.

        Returns:
            seq_features: Training sequences
            seq_targets: Training sequence targets
        """
        features = self.training_data['features']
        targets = self.training_data['targets']

        seq_features = []
        seq_targets = []

        for i in range(0, len(features) - sequence_length + 1, stride):
            seq_features.append(features[i:i + sequence_length])
            seq_targets.append(targets[i:i + sequence_length])

        seq_features = np.array(seq_features)
        seq_targets = np.array(seq_targets)

        print(f"Training sequences created: {seq_features.shape}")
        return seq_features, seq_targets

    def prepare_test_sequences(self, sequence_length: int, stride: int) -> Dict[str, Dict]:
        """
        Create sequences for each test well separately.

        Returns:
            Dict mapping well names to sequence data
        """
        test_sequences = {}

        for well_name, well_data in self.test_data.items():
            features = well_data['features']
            targets = well_data['targets']

            seq_features = []
            seq_targets = []

            for i in range(0, len(features) - sequence_length + 1, stride):
                seq_features.append(features[i:i + sequence_length])
                seq_targets.append(targets[i:i + sequence_length])

            if len(seq_features) > 0:
                test_sequences[well_name] = {
                    'features': np.array(seq_features),
                    'targets': np.array(seq_targets),
                    'original_data': well_data
                }
                print(f"Test sequences for {well_name}: {test_sequences[well_name]['features'].shape}")
            else:
                print(f"Warning: No sequences created for {well_name}")

        return test_sequences

    def evaluate_model_per_well(self, model, preprocessor, test_sequences: Dict) -> Dict[str, Dict]:
        """
        Evaluate model performance on each test well separately.

        Returns:
            Dict mapping well names to metrics
        """
        print(f"\nEvaluating model on {len(test_sequences)} test wells...")

        per_well_results = {}
        model.eval()
        device = next(model.parameters()).device

        for well_name, seq_data in test_sequences.items():
            print(f"   Evaluating {well_name}...")

            try:
                # Normalize test data using training preprocessor
                test_features = seq_data['features']
                test_targets = seq_data['targets']

                # Reshape for preprocessing
                test_feat_2d = test_features.reshape(-1, test_features.shape[-1])
                test_targ_2d = test_targets.reshape(-1, test_targets.shape[-1])

                # Transform using fitted preprocessor
                test_feat_norm = preprocessor.transform_features(test_feat_2d)
                test_targ_norm = preprocessor.transform_targets(test_targ_2d)

                # Reshape back to sequences
                test_feat_seq = test_feat_norm.reshape(test_features.shape)
                test_targ_seq = test_targ_norm.reshape(test_targets.shape)

                # Predict
                with torch.no_grad():
                    test_tensor = torch.FloatTensor(test_feat_seq).to(device)
                    predictions = model(test_tensor).cpu().numpy()

                # Transform back to original scale
                pred_2d = predictions.reshape(-1, predictions.shape[-1])
                targ_2d = test_targ_seq.reshape(-1, test_targ_seq.shape[-1])

                predictions_orig = preprocessor.inverse_transform_targets(pred_2d)
                targets_orig = preprocessor.inverse_transform_targets(targ_2d)

                # Flatten for metrics calculation
                pred_flat = predictions_orig.flatten()
                targ_flat = targets_orig.flatten()

                # Remove invalid values
                valid_mask = np.isfinite(pred_flat) & np.isfinite(targ_flat)
                pred_clean = pred_flat[valid_mask]
                targ_clean = targ_flat[valid_mask]

                if len(pred_clean) == 0:
                    print(f"   {well_name}: No valid predictions")
                    continue

                # Calculate metrics
                metrics = {
                    'rmse': np.sqrt(mean_squared_error(targ_clean, pred_clean)),
                    'mae': mean_absolute_error(targ_clean, pred_clean),
                    'r2': r2_score(targ_clean, pred_clean),
                    'correlation': np.corrcoef(pred_clean, targ_clean)[0, 1],
                    'n_points': len(pred_clean),
                    'mean_target': np.mean(targ_clean),
                    'std_target': np.std(targ_clean),
                    'mean_prediction': np.mean(pred_clean),
                    'std_prediction': np.std(pred_clean),
                    'bias': np.mean(pred_clean - targ_clean),
                    'std_residual': np.std(pred_clean - targ_clean)
                }

                per_well_results[well_name] = {
                    'metrics': metrics,
                    'predictions': pred_clean,
                    'targets': targ_clean,
                    'predictions_2d': predictions_orig,
                    'targets_2d': targets_orig,
                    'sequences': {
                        'features': test_feat_seq,
                        'targets': test_targ_seq,
                        'predictions': predictions
                    }
                }

                print(f"     RMSE: {metrics['rmse']:.2f}, R²: {metrics['r2']:.4f}, Points: {metrics['n_points']:,}")

            except Exception as e:
                print(f"   {well_name}: Error during evaluation - {e}")

        return per_well_results

    def calculate_aggregated_metrics(self, per_well_results: Dict) -> Dict:
        """
        Calculate aggregated statistics across test wells.

        Returns:
            Dict with mean ± std metrics
        """
        print(f"\nCalculating aggregated metrics across {len(per_well_results)} wells...")

        # Extract metrics from each well
        all_metrics = {}
        metric_names = ['rmse', 'mae', 'r2', 'correlation', 'bias', 'std_residual']

        for metric in metric_names:
            values = [results['metrics'][metric] for results in per_well_results.values()
                     if not np.isnan(results['metrics'][metric])]

            if len(values) > 0:
                all_metrics[metric] = {
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'values': values,
                    'n_wells': len(values)
                }
            else:
                all_metrics[metric] = {
                    'mean': np.nan,
                    'std': np.nan,
                    'min': np.nan,
                    'max': np.nan,
                    'values': [],
                    'n_wells': 0
                }

        # Print summary
        print("   Aggregated Results (Mean ± Std):")
        for metric, stats in all_metrics.items():
            if stats['n_wells'] > 0:
                print(f"     {metric.upper()}: {stats['mean']:.4f} ± {stats['std']:.4f} (n={stats['n_wells']})")

        return all_metrics

    def save_results(self, per_well_results: Dict, aggregated_metrics: Dict,
                    training_info: Dict = None):
        """
        Save blind test results to files.
        """
        print(f"\nSaving blind test results...")

        # Save detailed results as JSON
        results_data = {
            'timestamp': datetime.now().isoformat(),
            'training_well': self.training_well,
            'test_wells': list(per_well_results.keys()),
            'training_info': training_info or {},
            'per_well_metrics': {},
            'aggregated_metrics': aggregated_metrics,
            'config': self.config
        }

        # Extract just metrics for JSON (remove numpy arrays)
        for well_name, results in per_well_results.items():
            results_data['per_well_metrics'][well_name] = results['metrics']

        # Save JSON results
        json_path = self.blind_test_dir / "results" / "blind_test_metrics.json"
        with open(json_path, 'w') as f:
            json.dump(results_data, f, indent=2, default=str)
        print(f"   Results saved to: {json_path}")

        # Save detailed CSV
        csv_data = []
        for well_name, results in per_well_results.items():
            metrics = results['metrics']
            row = {'well_name': well_name}
            row.update(metrics)
            csv_data.append(row)

        csv_path = self.blind_test_dir / "results" / "per_well_detailed_results.csv"
        pd.DataFrame(csv_data).to_csv(csv_path, index=False)
        print(f"   Detailed CSV saved to: {csv_path}")

        return json_path, csv_path

    def create_blind_test_visualizations(self, per_well_results: Dict,
                                       aggregated_metrics: Dict):
        """
        Create comprehensive visualizations for blind test results.
        """
        print(f"\nCreating blind test visualizations...")

        # 1. Summary plot with all wells
        self._create_summary_plot(per_well_results, aggregated_metrics)

        # 2. Individual well plots
        self._create_individual_well_plots(per_well_results)

        # 3. Cross-well comparison
        self._create_cross_well_comparison(per_well_results, aggregated_metrics)

        # 4. Performance metrics table
        self._create_metrics_table(per_well_results, aggregated_metrics)

    def _create_summary_plot(self, per_well_results: Dict, aggregated_metrics: Dict):
        """Create main summary plot with parity plots for all wells."""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Blind Test Protocol Results - Summary', fontsize=16, fontweight='bold')

        colors = plt.cm.Set1(np.linspace(0, 1, len(per_well_results)))

        # Combined parity plot
        ax = axes[0, 0]
        all_targets = []
        all_predictions = []

        for i, (well_name, results) in enumerate(per_well_results.items()):
            targets = results['targets']
            predictions = results['predictions']

            clean_name = well_name.replace('_RP_INPUT.las', '').replace('_', '-')
            ax.scatter(targets, predictions, alpha=0.6, s=8, c=[colors[i]],
                      label=clean_name)

            all_targets.extend(targets)
            all_predictions.extend(predictions)

        all_targets = np.array(all_targets)
        all_predictions = np.array(all_predictions)

        # Perfect prediction line
        min_val, max_val = all_targets.min(), all_targets.max()
        ax.plot([min_val, max_val], [min_val, max_val], 'k--', lw=2, alpha=0.8,
                label='Perfect')

        overall_r2 = r2_score(all_targets, all_predictions)
        overall_rmse = np.sqrt(mean_squared_error(all_targets, all_predictions))

        ax.set_xlabel('Actual S-wave Velocity (m/s)', fontweight='bold')
        ax.set_ylabel('Predicted S-wave Velocity (m/s)', fontweight='bold')
        ax.set_title(f'All Test Wells - Parity Plot\n'
                    f'Overall: RMSE={overall_rmse:.2f}, R²={overall_r2:.4f}',
                    fontweight='bold')
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.grid(True, alpha=0.3)

        # Metrics comparison
        ax = axes[0, 1]
        metrics_to_plot = ['rmse', 'r2', 'correlation', 'mae']
        well_names = [w.replace('_RP_INPUT.las', '').replace('_', '-')
                     for w in per_well_results.keys()]

        x_pos = np.arange(len(well_names))
        bar_width = 0.8 / len(metrics_to_plot)

        for i, metric in enumerate(metrics_to_plot):
            values = [per_well_results[well]['metrics'][metric]
                     for well in per_well_results.keys()]

            bars = ax.bar(x_pos + i * bar_width, values, bar_width,
                         label=metric.upper(), alpha=0.8)

            # Add value labels on bars
            for bar, val in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + max(values)*0.01,
                       f'{val:.3f}', ha='center', va='bottom', fontsize=8)

        ax.set_xlabel('Test Wells', fontweight='bold')
        ax.set_ylabel('Metric Values', fontweight='bold')
        ax.set_title('Per-Well Performance Metrics', fontweight='bold')
        ax.set_xticks(x_pos + bar_width * (len(metrics_to_plot) - 1) / 2)
        ax.set_xticklabels(well_names)
        ax.legend()
        ax.grid(True, alpha=0.3, axis='y')

        # Error distribution
        ax = axes[1, 0]
        for i, (well_name, results) in enumerate(per_well_results.items()):
            residuals = results['predictions'] - results['targets']
            clean_name = well_name.replace('_RP_INPUT.las', '').replace('_', '-')

            ax.hist(residuals, bins=30, alpha=0.6, label=clean_name,
                   color=colors[i], density=True)

        ax.axvline(0, color='red', linestyle='--', alpha=0.8, linewidth=2)
        ax.set_xlabel('Residuals (m/s)', fontweight='bold')
        ax.set_ylabel('Density', fontweight='bold')
        ax.set_title('Residuals Distribution by Well', fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3)

        # Statistical summary table
        ax = axes[1, 1]
        ax.axis('off')

        # Create table data
        table_data = []
        headers = ['Metric', 'Mean ± Std', 'Min', 'Max', 'N Wells']

        for metric, stats in aggregated_metrics.items():
            if stats['n_wells'] > 0:
                row = [
                    metric.upper(),
                    f"{stats['mean']:.3f} ± {stats['std']:.3f}",
                    f"{stats['min']:.3f}",
                    f"{stats['max']:.3f}",
                    f"{stats['n_wells']}"
                ]
                table_data.append(row)

        # Create table
        table = ax.table(cellText=table_data, colLabels=headers,
                        cellLoc='center', loc='center',
                        bbox=[0, 0, 1, 1])
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1.2, 2)

        # Style table
        for (i, j), cell in table.get_celld().items():
            if i == 0:  # Header row
                cell.set_text_props(weight='bold')
                cell.set_facecolor('#4CAF50')
                cell.set_text_props(color='white')
            else:
                cell.set_facecolor('#f0f0f0' if i % 2 == 0 else 'white')

        ax.set_title('Aggregated Statistics Across Test Wells',
                    fontweight='bold', pad=20)

        plt.tight_layout()

        # Save plot
        summary_path = self.blind_test_dir / "visualizations" / "blind_test_summary.png"
        plt.savefig(summary_path, dpi=300, bbox_inches='tight')
        print(f"   Summary plot saved to: {summary_path}")
        plt.close()

    def _create_individual_well_plots(self, per_well_results: Dict):
        """Create individual detailed plots for each test well."""
        print("   Creating individual well plots...")

        for well_name, results in per_well_results.items():
            fig, axes = plt.subplots(2, 2, figsize=(14, 10))

            clean_name = well_name.replace('_RP_INPUT.las', '').replace('_', '-')
            fig.suptitle(f'Blind Test Results - Well {clean_name}',
                        fontsize=14, fontweight='bold')

            targets = results['targets']
            predictions = results['predictions']
            metrics = results['metrics']

            # Parity plot
            ax = axes[0, 0]
            ax.scatter(targets, predictions, alpha=0.6, s=5, c='blue')
            min_val, max_val = targets.min(), targets.max()
            ax.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2, alpha=0.8)
            ax.set_xlabel('Actual S-wave Velocity (m/s)')
            ax.set_ylabel('Predicted S-wave Velocity (m/s)')
            ax.set_title(f'Parity Plot\nRMSE: {metrics["rmse"]:.2f}, R²: {metrics["r2"]:.4f}')
            ax.grid(True, alpha=0.3)

            # Residuals plot
            ax = axes[0, 1]
            residuals = predictions - targets
            ax.scatter(targets, residuals, alpha=0.6, s=5, c='green')
            ax.axhline(0, color='red', linestyle='--', alpha=0.8)
            ax.axhline(np.mean(residuals), color='orange', linestyle=':', alpha=0.7)
            ax.set_xlabel('Actual S-wave Velocity (m/s)')
            ax.set_ylabel('Residuals (m/s)')
            ax.set_title(f'Residuals Plot\nBias: {metrics["bias"]:.2f}, Std: {metrics["std_residual"]:.2f}')
            ax.grid(True, alpha=0.3)

            # Residuals histogram
            ax = axes[1, 0]
            ax.hist(residuals, bins=30, alpha=0.7, color='purple', edgecolor='black')
            ax.axvline(0, color='red', linestyle='--', alpha=0.8)
            ax.axvline(np.mean(residuals), color='orange', linestyle=':', alpha=0.7)
            ax.set_xlabel('Residuals (m/s)')
            ax.set_ylabel('Frequency')
            ax.set_title('Residuals Distribution')
            ax.grid(True, alpha=0.3)

            # Statistics table
            ax = axes[1, 1]
            ax.axis('off')

            stats_text = f"""
Well: {clean_name}
Data Points: {metrics['n_points']:,}

Performance Metrics:
RMSE: {metrics['rmse']:.3f} m/s
MAE: {metrics['mae']:.3f} m/s
R² Score: {metrics['r2']:.4f}
Correlation: {metrics['correlation']:.4f}

Error Statistics:
Bias: {metrics['bias']:.3f} m/s
Std Residual: {metrics['std_residual']:.3f} m/s

Target Statistics:
Mean: {metrics['mean_target']:.1f} m/s
Std: {metrics['std_target']:.1f} m/s

Prediction Statistics:
Mean: {metrics['mean_prediction']:.1f} m/s
Std: {metrics['std_prediction']:.1f} m/s
            """.strip()

            ax.text(0.05, 0.95, stats_text, transform=ax.transAxes,
                   verticalalignment='top', fontfamily='monospace',
                   bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

            plt.tight_layout()

            # Save individual well plot
            safe_name = well_name.replace('.', '_').replace(' ', '_')
            well_path = (self.blind_test_dir / "visualizations" / "individual_wells"
                        / f"{safe_name}_results.png")
            plt.savefig(well_path, dpi=300, bbox_inches='tight')
            plt.close()

        print(f"     Individual well plots saved to: {self.blind_test_dir / 'visualizations' / 'individual_wells'}")

    def _create_cross_well_comparison(self, per_well_results: Dict, aggregated_metrics: Dict):
        """Create cross-well comparison visualization."""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Cross-Well Performance Comparison', fontsize=16, fontweight='bold')

        well_names = [w.replace('_RP_INPUT.las', '').replace('_', '-')
                     for w in per_well_results.keys()]

        # RMSE comparison
        ax = axes[0, 0]
        rmse_values = [per_well_results[well]['metrics']['rmse']
                      for well in per_well_results.keys()]
        rmse_mean = aggregated_metrics['rmse']['mean']
        rmse_std = aggregated_metrics['rmse']['std']

        bars = ax.bar(well_names, rmse_values, alpha=0.7, color='skyblue', edgecolor='navy')
        ax.axhline(rmse_mean, color='red', linestyle='-', alpha=0.8,
                   label=f'Mean: {rmse_mean:.2f}')
        ax.axhline(rmse_mean + rmse_std, color='red', linestyle='--', alpha=0.6,
                   label=f'Mean±Std: {rmse_mean:.2f}±{rmse_std:.2f}')
        ax.axhline(rmse_mean - rmse_std, color='red', linestyle='--', alpha=0.6)

        for bar, val in zip(bars, rmse_values):
            ax.text(bar.get_x() + bar.get_width()/2., bar.get_height() + max(rmse_values)*0.01,
                   f'{val:.2f}', ha='center', va='bottom', fontweight='bold')

        ax.set_ylabel('RMSE (m/s)', fontweight='bold')
        ax.set_title('RMSE by Test Well', fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3, axis='y')

        # R² comparison
        ax = axes[0, 1]
        r2_values = [per_well_results[well]['metrics']['r2']
                    for well in per_well_results.keys()]
        r2_mean = aggregated_metrics['r2']['mean']
        r2_std = aggregated_metrics['r2']['std']

        bars = ax.bar(well_names, r2_values, alpha=0.7, color='lightgreen', edgecolor='darkgreen')
        ax.axhline(r2_mean, color='red', linestyle='-', alpha=0.8,
                   label=f'Mean: {r2_mean:.3f}')
        ax.axhline(r2_mean + r2_std, color='red', linestyle='--', alpha=0.6,
                   label=f'Mean±Std: {r2_mean:.3f}±{r2_std:.3f}')
        ax.axhline(r2_mean - r2_std, color='red', linestyle='--', alpha=0.6)

        for bar, val in zip(bars, r2_values):
            ax.text(bar.get_x() + bar.get_width()/2., bar.get_height() + max(r2_values)*0.01,
                   f'{val:.3f}', ha='center', va='bottom', fontweight='bold')

        ax.set_ylabel('R² Score', fontweight='bold')
        ax.set_title('R² Score by Test Well', fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3, axis='y')
        ax.set_ylim(0, 1)

        # Data points comparison
        ax = axes[1, 0]
        data_points = [per_well_results[well]['metrics']['n_points']
                      for well in per_well_results.keys()]

        bars = ax.bar(well_names, data_points, alpha=0.7, color='orange', edgecolor='darkorange')

        for bar, val in zip(bars, data_points):
            ax.text(bar.get_x() + bar.get_width()/2., bar.get_height() + max(data_points)*0.01,
                   f'{val:,}', ha='center', va='bottom', fontweight='bold')

        ax.set_ylabel('Number of Data Points', fontweight='bold')
        ax.set_title('Data Points by Test Well', fontweight='bold')
        ax.grid(True, alpha=0.3, axis='y')

        # Correlation vs RMSE scatter
        ax = axes[1, 1]
        correlations = [per_well_results[well]['metrics']['correlation']
                       for well in per_well_results.keys()]

        for i, (well_name, rmse, corr) in enumerate(zip(well_names, rmse_values, correlations)):
            ax.scatter(corr, rmse, s=100, alpha=0.7, label=well_name)
            ax.annotate(well_name, (corr, rmse), xytext=(5, 5),
                       textcoords='offset points', fontsize=9)

        ax.set_xlabel('Correlation', fontweight='bold')
        ax.set_ylabel('RMSE (m/s)', fontweight='bold')
        ax.set_title('Correlation vs RMSE by Well', fontweight='bold')
        ax.grid(True, alpha=0.3)

        plt.tight_layout()

        # Save comparison plot
        comparison_path = self.blind_test_dir / "visualizations" / "cross_well_comparison.png"
        plt.savefig(comparison_path, dpi=300, bbox_inches='tight')
        print(f"   Cross-well comparison saved to: {comparison_path}")
        plt.close()

    def _create_metrics_table(self, per_well_results: Dict, aggregated_metrics: Dict):
        """Create a detailed metrics table visualization."""
        fig, ax = plt.subplots(1, 1, figsize=(14, 8))
        fig.suptitle('Blind Test Protocol - Detailed Metrics Table',
                    fontsize=16, fontweight='bold')

        ax.axis('off')

        # Prepare table data
        headers = ['Well', 'RMSE (m/s)', 'MAE (m/s)', 'R²', 'Correlation',
                  'Bias (m/s)', 'Std Residual (m/s)', 'Data Points']

        table_data = []
        for well_name, results in per_well_results.items():
            clean_name = well_name.replace('_RP_INPUT.las', '').replace('_', '-')
            metrics = results['metrics']

            row = [
                clean_name,
                f"{metrics['rmse']:.3f}",
                f"{metrics['mae']:.3f}",
                f"{metrics['r2']:.4f}",
                f"{metrics['correlation']:.4f}",
                f"{metrics['bias']:.3f}",
                f"{metrics['std_residual']:.3f}",
                f"{metrics['n_points']:,}"
            ]
            table_data.append(row)

        # Add aggregated statistics row
        agg_row = [
            'Mean ± Std',
            f"{aggregated_metrics['rmse']['mean']:.3f} ± {aggregated_metrics['rmse']['std']:.3f}",
            f"{aggregated_metrics['mae']['mean']:.3f} ± {aggregated_metrics['mae']['std']:.3f}",
            f"{aggregated_metrics['r2']['mean']:.4f} ± {aggregated_metrics['r2']['std']:.4f}",
            f"{aggregated_metrics['correlation']['mean']:.4f} ± {aggregated_metrics['correlation']['std']:.4f}",
            f"{aggregated_metrics['bias']['mean']:.3f} ± {aggregated_metrics['bias']['std']:.3f}",
            f"{aggregated_metrics['std_residual']['mean']:.3f} ± {aggregated_metrics['std_residual']['std']:.3f}",
            f"{sum(r['metrics']['n_points'] for r in per_well_results.values()):,}"
        ]
        table_data.append(agg_row)

        # Create table
        table = ax.table(cellText=table_data, colLabels=headers,
                        cellLoc='center', loc='center',
                        bbox=[0, 0, 1, 1])

        table.auto_set_font_size(False)
        table.set_fontsize(9)
        table.scale(1.0, 2.5)

        # Style table
        for (i, j), cell in table.get_celld().items():
            if i == 0:  # Header row
                cell.set_text_props(weight='bold')
                cell.set_facecolor('#2E86AB')
                cell.set_text_props(color='white')
            elif i == len(table_data):  # Aggregated stats row
                cell.set_text_props(weight='bold')
                cell.set_facecolor('#F24236')
                cell.set_text_props(color='white')
            else:
                cell.set_facecolor('#f0f0f0' if i % 2 == 0 else 'white')

        plt.tight_layout()

        # Save metrics table
        table_path = self.blind_test_dir / "visualizations" / "detailed_metrics_table.png"
        plt.savefig(table_path, dpi=300, bbox_inches='tight')
        print(f"   Detailed metrics table saved to: {table_path}")
        plt.close()

    def print_blind_test_summary(self, per_well_results: Dict, aggregated_metrics: Dict):
        """Print a comprehensive summary of blind test results."""
        print("\n" + "="*80)
        print("BLIND TEST PROTOCOL RESULTS SUMMARY")
        print("="*80)

        print(f"\nTraining Configuration:")
        print(f"   Training Well: {self.training_well}")
        print(f"   Training Data Points: {self.training_data['data_points']:,}")

        print(f"\nTest Wells Performance:")
        print(f"   Number of Test Wells: {len(per_well_results)}")

        # Individual well summary
        for well_name, results in per_well_results.items():
            clean_name = well_name.replace('_RP_INPUT.las', '').replace('_', '-')
            metrics = results['metrics']
            print(f"   {clean_name:>12}: RMSE={metrics['rmse']:6.2f}, R²={metrics['r2']:.4f}, "
                  f"Corr={metrics['correlation']:.4f}, Points={metrics['n_points']:,}")

        print(f"\nAggregated Statistics (Mean ± Std across {len(per_well_results)} wells):")
        for metric, stats in aggregated_metrics.items():
            if stats['n_wells'] > 0:
                print(f"   {metric.upper():>15}: {stats['mean']:8.4f} ± {stats['std']:.4f} "
                      f"(range: {stats['min']:.4f} - {stats['max']:.4f})")

        # Performance assessment
        avg_rmse = aggregated_metrics['rmse']['mean']
        avg_r2 = aggregated_metrics['r2']['mean']

        print(f"\nPerformance Assessment:")
        if avg_rmse < 50:
            rmse_assessment = "Excellent"
        elif avg_rmse < 100:
            rmse_assessment = "Good"
        elif avg_rmse < 200:
            rmse_assessment = "Fair"
        else:
            rmse_assessment = "Poor"

        if avg_r2 > 0.9:
            r2_assessment = "Excellent"
        elif avg_r2 > 0.8:
            r2_assessment = "Good"
        elif avg_r2 > 0.6:
            r2_assessment = "Fair"
        else:
            r2_assessment = "Poor"

        print(f"   RMSE Assessment: {rmse_assessment} ({avg_rmse:.2f} m/s)")
        print(f"   R² Assessment: {r2_assessment} ({avg_r2:.4f})")

        print(f"\nResults Location:")
        print(f"   Output Directory: {self.blind_test_dir}")
        print(f"   Individual Plots: {self.blind_test_dir / 'visualizations' / 'individual_wells'}")
        print(f"   Summary Data: {self.blind_test_dir / 'results'}")

        print("\n" + "="*80)