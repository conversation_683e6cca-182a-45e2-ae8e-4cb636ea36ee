#!/usr/bin/env python3
"""
Demonstration of the new rock physics models implementation.

This script shows how to use:
1. EmpiricalVPVS model (<PERSON> et al., 2024 Equation 6)
2. MultiparameterRegression model (<PERSON> et al., 2024 Equation 7)
3. Comparison with existing MudrockLine model
4. Model fitting capabilities
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from models.rock_physics import RockPhysicsFactory


def demonstrate_empirical_vpvs():
    """Demonstrate the EmpiricalVPVS model."""
    print("=" * 60)
    print("1. EMPIRICAL VP-VS RELATIONSHIP MODEL")
    print("=" * 60)
    
    # Create model using factory
    model = RockPhysicsFactory.create("empirical_vpvs")
    
    print(f"Model: {model.name}")
    print(f"Equation: {model.get_equation()}")
    print(f"Default parameters from <PERSON> et al. (2024):")
    print(f"  - a = {model.a:.4f}")
    print(f"  - b = {model.b:.4f}")
    
    # Test with typical velocity range
    vp_range = np.linspace(2.5, 6.0, 50)  # km/s
    vs_pred = model.predict(vp_range)
    
    print(f"\nPrediction examples:")
    for i in range(0, len(vp_range), 10):
        vp_val = vp_range[i]
        vs_val = vs_pred[i]
        print(f"  VP = {vp_val:.1f} km/s  ->  VS = {vs_val:.2f} km/s  (VS/VP = {vs_val/vp_val:.3f})")
    
    # Test fitting capability
    print(f"\nFitting example:")
    print(f"Original parameters: a = {model.a:.4f}, b = {model.b:.4f}")
    
    # Create synthetic data for fitting
    vp_fit = np.array([2.8, 3.2, 3.6, 4.0, 4.4, 4.8, 5.2])
    vs_true = 0.85 * np.power(vp_fit, 0.88)  # Different parameters
    vs_true += np.random.normal(0, 0.02, len(vs_true))  # Add noise
    
    model.fit(vp_fit, vs_true)
    print(f"Fitted parameters:   a = {model.a:.4f}, b = {model.b:.4f}")
    
    fit_quality = model.get_fit_quality()
    print(f"Fit RMSE (log space): {fit_quality.get('rmse_log_space', 'N/A'):.4f}")
    print(f"Updated equation: {model.get_equation()}")


def demonstrate_multiparameter():
    """Demonstrate the MultiparameterRegression model."""
    print("\n" + "=" * 60)
    print("2. MULTIPARAMETER REGRESSION MODEL")
    print("=" * 60)
    
    # Create model using factory
    model = RockPhysicsFactory.create("multiparameter")
    
    print(f"Model: {model.name}")
    print(f"Features: {model.feature_names}")
    print(f"Equation: {model.get_equation()}")
    
    # Create synthetic well log data
    n_samples = 20
    np.random.seed(42)
    
    well_data = {
        'GR': np.random.uniform(20, 120, n_samples),      # API units
        'DEN': np.random.uniform(2.0, 2.8, n_samples),   # g/cm³
        'VP': np.random.uniform(2.5, 5.5, n_samples),    # km/s
        'RES': np.random.uniform(1, 1000, n_samples)     # ohm.m
    }
    
    print(f"\nSynthetic well log data (first 5 samples):")
    print(f"{'Sample':<8} {'GR':<8} {'DEN':<8} {'VP':<8} {'RES':<8}")
    print("-" * 45)
    for i in range(5):
        print(f"{i+1:<8} {well_data['GR'][i]:<8.1f} {well_data['DEN'][i]:<8.2f} "
              f"{well_data['VP'][i]:<8.2f} {well_data['RES'][i]:<8.1f}")
    
    # Make predictions
    vs_pred = model.predict(well_data)
    
    print(f"\nPredicted VS values (first 5 samples):")
    for i in range(5):
        print(f"Sample {i+1}: VS = {vs_pred[i]:.3f} km/s")
    
    # Test with array input as well
    print(f"\nTesting array input format...")
    features_array = np.column_stack([well_data[f] for f in model.feature_names])
    vs_pred_array = model.predict(features_array)
    
    print(f"Consistency check: Dict and array predictions match = {np.allclose(vs_pred, vs_pred_array)}")
    
    # Show feature importance
    importance = model.get_feature_importance()
    print(f"\nFeature importance (absolute coefficient values):")
    for feature, imp in importance.items():
        print(f"  {feature}: {imp:.4f}")
    
    # Demonstrate fitting
    print(f"\nFitting demonstration:")
    
    # Create synthetic target VS based on known relationship
    true_coeffs = {'GR': 0.001, 'DEN': 0.6, 'VP': 0.4, 'RES': -0.0008, 'intercept': 0.5}
    vs_synthetic = (true_coeffs['GR'] * well_data['GR'] + 
                    true_coeffs['DEN'] * well_data['DEN'] + 
                    true_coeffs['VP'] * well_data['VP'] + 
                    true_coeffs['RES'] * np.log10(well_data['RES']) + 
                    true_coeffs['intercept'])
    vs_synthetic += np.random.normal(0, 0.05, n_samples)  # Add noise
    
    print("True coefficients:", true_coeffs)
    
    # Fit the model
    model.fit(well_data, vs_synthetic)
    
    print("Fitted coefficients:")
    for feature in model.feature_names + ['intercept']:
        print(f"  {feature}: {model.coefficients[feature]:.4f}")
    
    fit_quality = model.get_fit_quality()
    print(f"Fit quality: RMSE = {fit_quality['rmse']:.4f}, R² = {fit_quality['r2']:.4f}")


def compare_models():
    """Compare all three rock physics models."""
    print("\n" + "=" * 60)
    print("3. MODEL COMPARISON")
    print("=" * 60)
    
    # Create all models
    mudrock = RockPhysicsFactory.create("mudrock_line")
    empirical = RockPhysicsFactory.create("empirical_vpvs")
    multiparameter = RockPhysicsFactory.create("multiparameter")
    
    print("Model comparison for VP = 4.0 km/s:")
    print("-" * 40)
    
    vp_test = 4.0  # km/s
    
    # VP-only models
    vs_mudrock = mudrock.predict(np.array([vp_test]))[0]
    vs_empirical = empirical.predict(np.array([vp_test]))[0]
    
    print(f"MudrockLine:     VS = {vs_mudrock:.3f} km/s  (VS/VP = {vs_mudrock/vp_test:.3f})")
    print(f"EmpiricalVPVS:   VS = {vs_empirical:.3f} km/s  (VS/VP = {vs_empirical/vp_test:.3f})")
    
    # Multiparameter model with typical values
    typical_features = {
        'GR': np.array([60]),      # API
        'DEN': np.array([2.4]),    # g/cm³  
        'VP': np.array([vp_test]), # km/s
        'RES': np.array([50])      # ohm.m
    }
    
    vs_multi = multiparameter.predict(typical_features)[0]
    print(f"Multiparameter:  VS = {vs_multi:.3f} km/s  (VS/VP = {vs_multi/vp_test:.3f})")
    
    print(f"\nModel equations:")
    print(f"  {mudrock.name}: {mudrock.get_equation()}")
    print(f"  {empirical.name}: {empirical.get_equation()}")
    print(f"  {multiparameter.name}: VS = f(GR, DEN, VP, log10(RES))")
    
    # Show VP range comparison
    print(f"\nVP range comparison (2.5 - 6.0 km/s):")
    vp_range = np.array([2.5, 3.0, 3.5, 4.0, 4.5, 5.0, 5.5, 6.0])
    
    vs_mudrock_range = mudrock.predict(vp_range)
    vs_empirical_range = empirical.predict(vp_range)
    
    print(f"{'VP':<6} {'Mudrock':<8} {'Empirical':<10} {'Difference':<10}")
    print("-" * 36)
    
    for i, vp in enumerate(vp_range):
        vs_m = vs_mudrock_range[i]
        vs_e = vs_empirical_range[i]
        diff = abs(vs_e - vs_m)
        print(f"{vp:<6.1f} {vs_m:<8.3f} {vs_e:<10.3f} {diff:<10.3f}")


def demonstrate_standalone_usage():
    """Demonstrate how to use models as standalone functions."""
    print("\n" + "=" * 60)
    print("4. STANDALONE FUNCTION USAGE")
    print("=" * 60)
    
    print("The rock physics models can be used as standalone functions:")
    print("This meets the requirement to be 'callable independently'")
    
    # Example 1: Quick VP-VS prediction
    def quick_vpvs_prediction(vp_values, model_type="empirical_vpvs"):
        """Standalone function for quick VP-VS prediction."""
        model = RockPhysicsFactory.create(model_type)
        return model.predict(vp_values)
    
    vp_test = np.array([3.0, 4.0, 5.0])
    vs_result = quick_vpvs_prediction(vp_test)
    print(f"\nStandalone VP-VS prediction:")
    print(f"Input VP:  {vp_test} km/s")
    print(f"Output VS: {vs_result} km/s")
    
    # Example 2: Multiparameter prediction function
    def multiparameter_vs_prediction(gr, den, vp, res):
        """Standalone function for multiparameter VS prediction."""
        model = RockPhysicsFactory.create("multiparameter")
        features = {'GR': gr, 'DEN': den, 'VP': vp, 'RES': res}
        return model.predict(features)
    
    vs_multi_result = multiparameter_vs_prediction(
        gr=np.array([50, 70]), 
        den=np.array([2.3, 2.5]), 
        vp=np.array([3.5, 4.5]), 
        res=np.array([20, 100])
    )
    
    print(f"\nStandalone multiparameter prediction:")
    print(f"VS results: {vs_multi_result} km/s")
    
    # Example 3: Model fitting as standalone function
    def fit_empirical_model_to_well(vp_data, vs_data):
        """Standalone function to fit empirical model to well data."""
        model = RockPhysicsFactory.create("empirical_vpvs")
        model.fit(vp_data, vs_data)
        return model
    
    # Test data
    vp_well = np.array([2.8, 3.2, 3.8, 4.2, 4.8])
    vs_well = np.array([1.6, 1.8, 2.1, 2.4, 2.7])
    
    fitted_model = fit_empirical_model_to_well(vp_well, vs_well)
    print(f"\nStandalone model fitting:")
    print(f"Fitted equation: {fitted_model.get_equation()}")
    
    print(f"\nKey advantages of this implementation:")
    print(f"✓ Standalone functions - can be called independently")
    print(f"✓ Flexible input formats - arrays or dictionaries")
    print(f"✓ Easy to add new models - just register in factory")
    print(f"✓ Backward compatible - existing code still works")
    print(f"✓ Comprehensive - supports fitting and prediction")


def create_visualization():
    """Create a visualization comparing the models."""
    print("\n" + "=" * 60)
    print("5. CREATING MODEL COMPARISON VISUALIZATION")
    print("=" * 60)
    
    # Create VP range
    vp_range = np.linspace(2.5, 6.0, 100)
    
    # Get predictions from all VP-based models
    mudrock = RockPhysicsFactory.create("mudrock_line")
    empirical = RockPhysicsFactory.create("empirical_vpvs")
    
    vs_mudrock = mudrock.predict(vp_range)
    vs_empirical = empirical.predict(vp_range)
    
    # Create plot
    plt.figure(figsize=(12, 8))
    
    # Plot 1: Model comparison
    plt.subplot(2, 2, 1)
    plt.plot(vp_range, vs_mudrock, 'b-', label='Mudrock Line', linewidth=2)
    plt.plot(vp_range, vs_empirical, 'r-', label='Empirical VP-VS', linewidth=2)
    plt.plot(vp_range, vp_range * 0.577, 'k--', label='Theoretical VS/VP=0.577', alpha=0.7)
    plt.xlabel('VP (km/s)')
    plt.ylabel('VS (km/s)')
    plt.title('Rock Physics Models Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot 2: VS/VP ratios
    plt.subplot(2, 2, 2)
    plt.plot(vp_range, vs_mudrock/vp_range, 'b-', label='Mudrock Line', linewidth=2)
    plt.plot(vp_range, vs_empirical/vp_range, 'r-', label='Empirical VP-VS', linewidth=2)
    plt.axhline(y=0.577, color='k', linestyle='--', alpha=0.7, label='Theoretical 0.577')
    plt.xlabel('VP (km/s)')
    plt.ylabel('VS/VP Ratio')
    plt.title('VS/VP Ratio vs VP')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot 3: Multiparameter sensitivity
    plt.subplot(2, 2, 3)
    multiparameter = RockPhysicsFactory.create("multiparameter")
    
    # Fixed values
    fixed_features = {
        'GR': np.array([60]),    # API
        'DEN': np.array([2.4]),  # g/cm³
        'RES': np.array([50])    # ohm.m
    }
    
    vs_multi = []
    for vp_val in vp_range:
        features = fixed_features.copy()
        features['VP'] = np.array([vp_val])
        vs_multi.append(multiparameter.predict(features)[0])
    
    vs_multi = np.array(vs_multi)
    plt.plot(vp_range, vs_multi, 'g-', label='Multiparameter (GR=60, DEN=2.4, RES=50)', linewidth=2)
    plt.plot(vp_range, vs_empirical, 'r--', label='Empirical VP-VS', alpha=0.7)
    plt.xlabel('VP (km/s)')
    plt.ylabel('VS (km/s)')
    plt.title('Multiparameter Model with Fixed Parameters')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot 4: Feature sensitivity for multiparameter model
    plt.subplot(2, 2, 4)
    
    # Test sensitivity to different parameters
    vp_fixed = 4.0  # km/s
    gr_range = np.linspace(20, 120, 50)
    
    vs_gr_sensitivity = []
    for gr_val in gr_range:
        features = {
            'GR': np.array([gr_val]),
            'DEN': np.array([2.4]),
            'VP': np.array([vp_fixed]),
            'RES': np.array([50])
        }
        vs_gr_sensitivity.append(multiparameter.predict(features)[0])
    
    plt.plot(gr_range, vs_gr_sensitivity, 'g-', label='VS vs GR (VP=4.0)', linewidth=2)
    plt.xlabel('Gamma Ray (API)')
    plt.ylabel('VS (km/s)')
    plt.title('Multiparameter Model: GR Sensitivity')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Save the plot
    output_path = "rock_physics_models_comparison.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"Visualization saved to: {output_path}")
    
    # Show basic statistics
    print(f"\nModel statistics (VP range 2.5-6.0 km/s):")
    print(f"Mudrock Line:    VS range = {vs_mudrock.min():.3f} - {vs_mudrock.max():.3f} km/s")
    print(f"Empirical VP-VS: VS range = {vs_empirical.min():.3f} - {vs_empirical.max():.3f} km/s")
    print(f"Multiparameter:  VS range = {vs_multi.min():.3f} - {vs_multi.max():.3f} km/s")


def main():
    """Main demonstration function."""
    print("Rock Physics Models Implementation Demonstration")
    print("Based on Zhao et al. (2024) - Equations 6 and 7")
    
    try:
        demonstrate_empirical_vpvs()
        demonstrate_multiparameter()
        compare_models()
        demonstrate_standalone_usage()
        create_visualization()
        
        print("\n" + "=" * 60)
        print("✅ DEMONSTRATION COMPLETED SUCCESSFULLY")
        print("=" * 60)
        print("\nSummary of implemented features:")
        print("✓ EmpiricalVPVS model (Equation 6): VS = a * VP^b")
        print("✓ MultiparameterRegression model (Equation 7): VS = f(GR, DEN, VP, log10(RES))")
        print("✓ Standalone, callable functions")
        print("✓ Flexible input formats (arrays and dictionaries)")
        print("✓ Model fitting capabilities")
        print("✓ Factory pattern for easy model creation")
        print("✓ Backward compatibility with existing code")
        print("✓ Comprehensive testing and validation")
        print("✓ Automatic resistivity log transformation")
        print("✓ Both single and multi-parameter support")
        
        print(f"\nNext steps:")
        print(f"1. Use these models in your training pipeline")
        print(f"2. Fit models to your specific well data")
        print(f"3. Experiment with transfer learning using these physics models")
        print(f"4. Add more rock physics models as needed")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure matplotlib is installed: pip install matplotlib")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()