from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Union
import numpy as np

class RockPhysicsModel(ABC):
    """Abstract base class for rock physics models.
    
    This class provides a common interface for all rock physics models,
    supporting both single-parameter (VP-only) and multi-parameter inputs.
    """

    def __init__(self, name: str, **kwargs):
        self.name = name
        self.params = kwargs
        self._fitted = False

    @abstractmethod
    def predict(self, features: Union[np.ndarray, Dict[str, np.ndarray]], **kwargs) -> np.ndarray:
        """Predict S-wave velocity from input features.
        
        Args:
            features: Input features. Can be:
                - np.ndarray: For single-parameter models (VP values)
                - Dict[str, np.ndarray]: For multi-parameter models with named features
            **kwargs: Additional model-specific parameters
            
        Returns:
            np.ndarray: Predicted S-wave velocities
        """
        pass

    @abstractmethod
    def fit(self, features: Union[np.ndarray, Dict[str, np.ndarray]], vs: np.ndar<PERSON>, **kwargs) -> None:
        """Fit model parameters from training data.
        
        Args:
            features: Input features in same format as predict()
            vs: Target S-wave velocity values
            **kwargs: Additional fitting parameters
        """
        pass

    @property
    def fitted(self) -> bool:
        return self._fitted

    def get_params(self) -> Dict[str, Any]:
        """Return model parameters."""
        return self.params

    def set_params(self, **params) -> None:
        """Set model parameters."""
        self.params.update(params)
